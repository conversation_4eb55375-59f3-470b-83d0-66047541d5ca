import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

class AppFooter extends StatelessWidget {
  const AppFooter({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentYear = DateTime.now().year;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Main footer content
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Copyright section
              Expanded(
                child: Text(
                  '${'common.copyright'.tr()} ${'logo'.tr()} $currentYear',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              
              // Links section (hidden on mobile)
              if (MediaQuery.of(context).size.width >= 640)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildFooterLink(
                      context,
                      'About',
                      () {
                        // TODO: Navigate to about page
                      },
                    ),
                    const SizedBox(width: 16),
                    _buildFooterLink(
                      context,
                      'Privacy Policy',
                      () {
                        // TODO: Navigate to privacy policy
                      },
                    ),
                    const SizedBox(width: 16),
                    _buildFooterLink(
                      context,
                      'Licensing',
                      () {
                        // TODO: Navigate to licensing
                      },
                    ),
                    const SizedBox(width: 16),
                    _buildFooterLink(
                      context,
                      'Contact',
                      () {
                        // TODO: Navigate to contact
                      },
                    ),
                  ],
                ),
            ],
          ),
          
          // Mobile links (shown only on mobile)
          if (MediaQuery.of(context).size.width < 640) ...[
            const SizedBox(height: 12),
            Wrap(
              alignment: WrapAlignment.center,
              spacing: 16,
              runSpacing: 8,
              children: [
                _buildFooterLink(
                  context,
                  'About',
                  () {
                    // TODO: Navigate to about page
                  },
                ),
                _buildFooterLink(
                  context,
                  'Privacy Policy',
                  () {
                    // TODO: Navigate to privacy policy
                  },
                ),
                _buildFooterLink(
                  context,
                  'Licensing',
                  () {
                    // TODO: Navigate to licensing
                  },
                ),
                _buildFooterLink(
                  context,
                  'Contact',
                  () {
                    // TODO: Navigate to contact
                  },
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFooterLink(
    BuildContext context,
    String text,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        child: Text(
          text,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
            decoration: TextDecoration.underline,
            decorationColor: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
          ),
        ),
      ),
    );
  }
}
