class User {
  final int id;
  final String username;
  final String? firstname;
  final String? lastname;
  final String? email;
  final String? phone;
  final String? address;
  final String? city;
  final String? notes;
  final String? site;
  final int? profileId;
  final String? balance;
  final String? loanBalance;
  final String? lastOnline;
  final int parentId;
  final String? staticIp;
  final int? enabled;
  final String? company;
  final int simultaneousSessions;
  final String? contractId;
  final String? createdAt;
  final String? nationalId;
  final String? mikrotikIpv6Prefix;
  final int? groupId;
  final double? gpsLat;
  final double? gpsLng;
  final String? street;
  final int? siteId;
  final int nRow;
  final int remainingDays;
  final String? expiration;
  final String? profile;
  final String status;
  final int onlineStatus;

  User({
    required this.id,
    required this.username,
    this.firstname,
    this.lastname,
    this.email,
    this.phone,
    this.address,
    this.city,
    this.notes,
    this.site,
    this.profileId,
    this.balance,
    this.loanBalance,
    this.lastOnline,
    required this.parentId,
    this.staticIp,
    this.enabled,
    this.company,
    required this.simultaneousSessions,
    this.contractId,
    this.createdAt,
    this.nationalId,
    this.mikrotikIpv6Prefix,
    this.groupId,
    this.gpsLat,
    this.gpsLng,
    this.street,
    this.siteId,
    required this.nRow,
    required this.remainingDays,
    this.expiration,
    this.profile,
    required this.status,
    required this.onlineStatus,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      firstname: json['firstname'],
      lastname: json['lastname'],
      email: json['email'],
      phone: json['phone'],
      address: json['address'],
      city: json['city'],
      notes: json['notes'],
      site: json['site'],
      profileId: json['profile_id'],
      balance: json['balance']?.toString(),
      loanBalance: json['loan_balance']?.toString(),
      lastOnline: json['last_online'],
      parentId: json['parent_id'] ?? 0,
      staticIp: json['static_ip'],
      enabled: json['enabled'],
      company: json['company'],
      simultaneousSessions: json['simultaneous_sessions'] ?? 0,
      contractId: json['contract_id'],
      createdAt: json['created_at'],
      nationalId: json['national_id'],
      mikrotikIpv6Prefix: json['mikrotik_ipv6_prefix'],
      groupId: json['group_id'],
      gpsLat: json['gps_lat']?.toDouble(),
      gpsLng: json['gps_lng']?.toDouble(),
      street: json['street'],
      siteId: json['site_id'],
      nRow: json['n_row'] ?? 0,
      remainingDays: json['remaining_days'] ?? 0,
      expiration: json['expiration'],
      profile: json['profile'],
      status: json['status']?.toString() ?? 'unknown',
      onlineStatus: json['online_status'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'firstname': firstname,
      'lastname': lastname,
      'email': email,
      'phone': phone,
      'address': address,
      'city': city,
      'notes': notes,
      'site': site,
      'profile_id': profileId,
      'balance': balance,
      'loan_balance': loanBalance,
      'last_online': lastOnline,
      'parent_id': parentId,
      'static_ip': staticIp,
      'enabled': enabled,
      'company': company,
      'simultaneous_sessions': simultaneousSessions,
      'contract_id': contractId,
      'created_at': createdAt,
      'national_id': nationalId,
      'mikrotik_ipv6_prefix': mikrotikIpv6Prefix,
      'group_id': groupId,
      'gps_lat': gpsLat,
      'gps_lng': gpsLng,
      'street': street,
      'site_id': siteId,
      'n_row': nRow,
      'remaining_days': remainingDays,
      'expiration': expiration,
      'profile': profile,
      'status': status,
      'online_status': onlineStatus,
    };
  }

  // Helper getters for backward compatibility
  String get fullName => '${firstname ?? ''} ${lastname ?? ''}'.trim();
  bool get isActive => enabled == 1;
  bool get isOnline => onlineStatus == 1;
  String? get lastLogin => lastOnline;
}
