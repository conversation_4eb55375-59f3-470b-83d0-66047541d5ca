import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../core/auth/auth_service.dart';
import '../../../core/di/injection_container.dart' as di;
import '../../../core/network/api_service.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/widgets/loading.dart';
import '../../../shared/widgets/toast.dart';
import '../services/users_service.dart';
import '../../../shared/models/user_model.dart';
import 'user_details_screen.dart';

class AllUsersScreen extends StatefulWidget {
  const AllUsersScreen({super.key});

  @override
  State<AllUsersScreen> createState() => _AllUsersScreenState();
}

class _AllUsersScreenState extends State<AllUsersScreen> {
  final UsersService _usersService = UsersService(di.sl<ApiService>());
  List<User> _users = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final users = await _usersService.getAllUsers();
      setState(() {
        _users = users;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ToastService.showError('Failed to load users: ${e.toString()}');
    }
  }

  List<User> get _filteredUsers {
    if (_searchQuery.isEmpty) {
      return _users;
    }

    final query = _searchQuery.toLowerCase();
    return _users.where((user) {
      return user.username.toLowerCase().contains(query) ||
          (user.email?.toLowerCase().contains(query) ?? false) ||
          user.fullName.toLowerCase().contains(query);
    }).toList();
  }

  Future<void> _deleteUser(User user) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('users.delete_user'.tr()),
        content: Text('users.delete_confirm'.tr(args: [user.username])),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('common.cancel'.tr()),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'common.delete'.tr(),
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await _usersService.deleteUser(user.id);
        ToastService.showSuccess('users.delete_success'.tr());
        _loadUsers();
      } catch (e) {
        ToastService.showError('users.delete_error'.tr(args: [e.toString()]));
      }
    }
  }

  Future<void> _toggleUserStatus(User user) async {
    try {
      if (user.isActive) {
        await _usersService.deactivateUser(user.id);
        ToastService.showSuccess('users.deactivate_success'.tr());
      } else {
        await _usersService.activateUser(user.id);
        ToastService.showSuccess('users.activate_success'.tr());
      }
      _loadUsers();
    } catch (e) {
      ToastService.showError(
        'users.status_change_error'.tr(args: [e.toString()]),
      );
    }
  }

  void _viewUserDetails(User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserDetailsScreen(userId: user.id),
      ),
    ).then((_) => _loadUsers());
  }

  void _addNewUser() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const UserDetailsScreen(isNewUser: true),
      ),
    ).then((_) => _loadUsers());
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authService = di.sl<AuthService>();
    final currentUser = authService.currentUser;

    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'users.search'.tr(),
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                CustomButton(
                  onPressed: _addNewUser,
                  text: 'users.add_user'.tr(),
                  icon: Icons.add,
                  type: ButtonType.primary,
                ),
              ],
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: LoadingIndicator())
                : _filteredUsers.isEmpty
                ? Center(
                    child: Text(
                      _searchQuery.isEmpty
                          ? 'users.no_users'.tr()
                          : 'users.no_search_results'.tr(),
                      style: theme.textTheme.titleMedium,
                    ),
                  )
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: CustomCard(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: DataTable(
                          columns: [
                            DataColumn(label: Text('users.username'.tr())),
                            DataColumn(label: Text('users.email'.tr())),
                            DataColumn(label: Text('users.full_name'.tr())),
                            DataColumn(label: Text('users.status'.tr())),
                            DataColumn(label: Text('users.last_login'.tr())),
                            DataColumn(label: Text('common.actions'.tr())),
                          ],
                          rows: _filteredUsers.map((user) {
                            return DataRow(
                              cells: [
                                DataCell(Text(user.username)),
                                DataCell(Text(user.email ?? '')),
                                DataCell(Text(user.fullName)),
                                DataCell(
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: user.isActive
                                          ? Colors.green.withAlpha(
                                              (255 * 0.2).round(),
                                            )
                                          : Colors.red.withAlpha(
                                              (255 * 0.2).round(),
                                            ),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Text(
                                      user.isActive
                                          ? 'users.active'.tr()
                                          : 'users.inactive'.tr(),
                                      style: TextStyle(
                                        color: user.isActive
                                            ? Colors.green
                                            : Colors.red,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                DataCell(
                                  Text(
                                    user.lastLogin != null
                                        ? DateFormat.yMd().add_Hm().format(
                                            DateTime.parse(user.lastLogin!),
                                          )
                                        : '-',
                                  ),
                                ),
                                DataCell(
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Icons.visibility),
                                        tooltip: 'users.view'.tr(),
                                        onPressed: () => _viewUserDetails(user),
                                      ),
                                      IconButton(
                                        icon: Icon(
                                          user.isActive
                                              ? Icons.toggle_on
                                              : Icons.toggle_off,
                                          color: user.isActive
                                              ? Colors.green
                                              : Colors.grey,
                                        ),
                                        tooltip: user.isActive
                                            ? 'users.deactivate'.tr()
                                            : 'users.activate'.tr(),
                                        onPressed: () =>
                                            _toggleUserStatus(user),
                                      ),
                                      // Only allow deleting other users, not yourself
                                      if (currentUser?.id != user.id)
                                        IconButton(
                                          icon: const Icon(
                                            Icons.delete,
                                            color: Colors.red,
                                          ),
                                          tooltip: 'users.delete'.tr(),
                                          onPressed: () => _deleteUser(user),
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadUsers,
        tooltip: 'common.refresh'.tr(),
        child: const Icon(Icons.refresh),
      ),
    );
  }
}
