{"app": {"title": "Abraji Internet", "subtitle": "Reliable Internet Reseller in Iraq"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "domain": "Domain", "remember_me": "Remember me", "forgot_password": "Forgot password?", "login_error": "Invalid username or password", "login_success": "Login successful"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome", "total_users": "Total Users", "online_users": "Online Users", "total_revenue": "Total Revenue", "recent_activities": "Recent Activities", "maintenanceExpenses": "Maintenance Expenses", "expenseTransactions": "Expense Transactions", "generalExpenses": "General Expenses", "used": "Used", "remaining": "Remaining"}, "users": {"title": "Users", "all_users": "All Users", "online_users": "Online Users", "create_user": "Create User", "add_user": "Add User", "edit_user": "Edit User", "user_details": "User Details", "username": "Username", "email": "Email", "status": "Status", "actions": "Actions", "search": "Search", "active": "Active", "inactive": "Inactive", "profile": "Profile", "sessions": "Sessions", "traffic": "Traffic", "invoices": "Invoices", "no_users": "No users found"}, "cards": {"title": "Cards", "subtitle": "Manage internet and service cards", "createCard": "Create Card", "createNewCard": "Create New Card", "createCardDescription": "Add a new card to the system", "cardDetails": "Card Details", "cardName": "Card Name", "cardNameHint": "Enter card name", "cardNameRequired": "Card name is required", "description": "Description", "descriptionHint": "Enter card description", "descriptionRequired": "Description is required", "category": "Category", "totalCards": "Total Cards", "totalCardsRequired": "Total cards is required", "usedCards": "Used Cards", "remainingCards": "Remaining Cards", "price": "Price", "priceRequired": "Price is required", "invalidPrice": "Invalid price", "validityDays": "Validity Days", "validityDaysRequired": "Validity days is required", "invalidDays": "Invalid number of days", "days": "days", "features": "Features", "selectFeatures": "Select available features", "basicInformation": "Basic Information", "configuration": "Configuration", "usageProgress": "Usage Progress", "usageStatistics": "Usage Statistics", "cardsList": "Cards List", "cards": "cards", "used": "Used", "remaining": "Remaining", "available": "Available", "active": "Active", "lowStock": "Low Stock", "usedBy": "Used by", "searchHint": "Search cards...", "foundRecords": "Found records:", "noCardsFound": "No cards found", "noCardsDescription": "No cards are currently available", "cardNotFound": "Card not found", "deleteCard": "Delete Card", "deleteCardConfirmation": "Are you sure you want to delete this card?", "cardCreatedSuccessfully": "Card created successfully", "createCardError": "Error creating card", "invalidNumber": "Invalid number"}, "sidebar": {"dashboard": "Dashboard", "users": "Users", "usersList": "Users List", "onlineUsers": "Online Users", "createUser": "Create User", "cards": "Cards", "allCards": "All Cards", "createCard": "Create Card", "invoices": "Invoices", "allInvoices": "All Invoices", "createInvoice": "Create Invoice", "resources": "Resources", "expenses": "Expenses", "debts": "Debts", "allDebts": "All Debts", "debtHistory": "Debt History", "wallet": "Wallet", "settings": "Settings"}, "settings": {"title": "Settings", "language": "Language", "theme": "Theme", "notifications": "Notifications", "profile": "Profile", "security": "Security"}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "loading": "Loading...", "no_data": "No data available", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information"}}