import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../features/auth/presentation/bloc/auth_bloc.dart';
import '../features/auth/presentation/bloc/auth_state.dart';
import '../features/auth/screens/login_screen.dart';
import '../features/dashboard/screens/dashboard_screen.dart';
import '../features/users/screens/all_users_screen.dart';
import '../features/users/screens/online_users_screen.dart';
import '../features/users/screens/user_details_screen.dart';
import '../features/settings/screens/settings_screen.dart';
import '../shared/widgets/layouts/main_layout.dart';

class AppRouter {
  static GoRouter getRouter(BuildContext context, VoidCallback toggleTheme) {
    return GoRouter(
      initialLocation: '/',
      redirect: (context, state) {
        final authState = context.read<AuthBloc>().state;
        final bool isLoggedIn = authState is AuthSuccess;
        final bool isLoginRoute = state.uri.path == '/login';

        // If not logged in and not on login page, redirect to login
        if (!isLoggedIn && !isLoginRoute) {
          return '/login';
        }

        // If logged in and on login page, redirect to dashboard
        if (isLoggedIn && isLoginRoute) {
          return '/';
        }

        // No redirection needed
        return null;
      },
      routes: [
        // Auth routes
        GoRoute(
          path: '/login',
          builder: (context, state) => LoginScreen(toggleTheme: toggleTheme),
        ),

        // Main app routes with MainLayout wrapper
        ShellRoute(
          builder: (context, state, child) =>
              MainLayout(toggleTheme: toggleTheme, child: child),
          routes: [
            // Dashboard route
            GoRoute(
              path: '/',
              builder: (context, state) =>
                  DashboardScreen(toggleTheme: toggleTheme),
            ),

            // User routes
            GoRoute(
              path: '/users',
              builder: (context, state) => const AllUsersScreen(),
            ),
            GoRoute(
              path: '/users/online',
              builder: (context, state) => const OnlineUsersScreen(),
            ),
            GoRoute(
              path: '/users/:userId',
              builder: (context, state) {
                final userIdStr = state.pathParameters['userId'];
                final userId = int.tryParse(userIdStr ?? '');
                return UserDetailsScreen(userId: userId);
              },
            ),
            GoRoute(
              path: '/users/new',
              builder: (context, state) =>
                  const UserDetailsScreen(isNewUser: true),
            ),

            // Cards routes (matching abraji_web structure)
            GoRoute(
              path: '/cards',
              builder: (context, state) =>
                  const Placeholder(), // TODO: Implement cards screen
            ),
            GoRoute(
              path: '/cards/new',
              builder: (context, state) =>
                  const Placeholder(), // TODO: Implement create card screen
            ),

            // Invoices routes (matching abraji_web structure)
            GoRoute(
              path: '/invoices',
              builder: (context, state) =>
                  const Placeholder(), // TODO: Implement invoices screen
            ),
            GoRoute(
              path: '/invoices/new',
              builder: (context, state) =>
                  const Placeholder(), // TODO: Implement create invoice screen
            ),

            // Settings route
            GoRoute(
              path: '/settings',
              builder: (context, state) =>
                  SettingsScreen(toggleTheme: toggleTheme),
            ),
          ],
        ),
      ],
      errorBuilder: (context, state) => Scaffold(
        appBar: AppBar(title: const Text('Page Not Found')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Page not found: ${state.uri.path}',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.go('/'),
                child: const Text('Go to Dashboard'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
