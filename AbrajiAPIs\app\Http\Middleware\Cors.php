<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class Cors
{
    public function handle(Request $request, Closure $next)
    {
        // Handle preflight OPTIONS requests
        if ($request->getMethod() === 'OPTIONS') {
            $response = response('', 200);
        } else {
            $response = $next($request);
        }

        // Allow multiple origins for development
        $origin = $request->headers->get('Origin');

        // Allow localhost and 127.0.0.1 on any port for development
        if ($origin && (
            strpos($origin, 'http://localhost:') === 0 ||
            strpos($origin, 'http://127.0.0.1:') === 0 ||
            strpos($origin, 'https://localhost:') === 0 ||
            strpos($origin, 'https://127.0.0.1:') === 0
        )) {
            $response->headers->set('Access-Control-Allow-Origin', $origin);
        } else {
            $response->headers->set('Access-Control-Allow-Origin', '*');
        }

        $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN, X-API-Domain, Accept-Language');
        $response->headers->set('Access-Control-Allow-Credentials', 'true');

        return $response;
    }
} 