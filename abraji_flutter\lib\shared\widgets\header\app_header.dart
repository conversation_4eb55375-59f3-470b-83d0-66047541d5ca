import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../core/auth/auth_service.dart';
import '../../../core/di/injection_container.dart' as di;
import '../logo.dart';

class AppHeader extends StatefulWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;
  final VoidCallback toggleTheme;

  const AppHeader({
    super.key,
    required this.scaffoldKey,
    required this.toggleTheme,
  });

  @override
  State<AppHeader> createState() => _AppHeaderState();
}

class _AppHeaderState extends State<AppHeader> {
  bool _dropdownOpen = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authService = di.sl<AuthService>();
    final user = authService.currentUser;
    final isArabic = context.locale.languageCode == 'ar';

    return Container(
      height: 64, // Fixed header height like abraji_web
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            // Mobile menu button
            if (MediaQuery.of(context).size.width < 768)
              IconButton(
                icon: const Icon(Icons.menu),
                onPressed: () {
                  widget.scaffoldKey.currentState?.openDrawer();
                },
              ),

            // Logo section
            Row(
              children: [
                const LogoIcon(size: 32),
                const SizedBox(width: 8),
                const LogoText(fontSize: 18, color: null),
              ],
            ),

            const Spacer(),

            // Right side actions
            Row(
              children: [
                // Notifications
                IconButton(
                  icon: const Icon(Icons.notifications_outlined),
                  onPressed: () {
                    // TODO: Show notifications
                  },
                ),

                // Language toggle
                IconButton(
                  icon: Icon(isArabic ? Icons.language : Icons.translate),
                  onPressed: () {
                    final newLocale = isArabic 
                        ? const Locale('en') 
                        : const Locale('ar');
                    context.setLocale(newLocale);
                  },
                ),

                // Theme toggle
                IconButton(
                  icon: Icon(
                    theme.brightness == Brightness.dark
                        ? Icons.light_mode
                        : Icons.dark_mode,
                  ),
                  onPressed: widget.toggleTheme,
                ),

                const SizedBox(width: 8),

                // User dropdown
                PopupMenuButton<String>(
                  offset: const Offset(0, 40),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.dividerColor,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircleAvatar(
                          radius: 16,
                          backgroundImage: user?.username != null
                              ? NetworkImage(
                                  'https://ui-avatars.com/api/?name=${user!.username}&background=random&bold=true&color=000000',
                                )
                              : null,
                          child: user?.username == null
                              ? const Icon(Icons.person, size: 16)
                              : null,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          user?.username ?? 'User',
                          style: theme.textTheme.bodyMedium,
                        ),
                        const SizedBox(width: 4),
                        const Icon(Icons.keyboard_arrow_down, size: 16),
                      ],
                    ),
                  ),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'profile',
                      child: Row(
                        children: [
                          const Icon(Icons.person_outline),
                          const SizedBox(width: 12),
                          Text('header.profile'.tr()),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'settings',
                      child: Row(
                        children: [
                          const Icon(Icons.settings_outlined),
                          const SizedBox(width: 12),
                          Text('header.settings'.tr()),
                        ],
                      ),
                    ),
                    const PopupMenuDivider(),
                    PopupMenuItem(
                      value: 'logout',
                      child: Row(
                        children: [
                          const Icon(Icons.logout, color: Colors.red),
                          const SizedBox(width: 12),
                          Text(
                            'common.logout'.tr(),
                            style: const TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) {
                    switch (value) {
                      case 'profile':
                        // TODO: Navigate to profile
                        break;
                      case 'settings':
                        // TODO: Navigate to settings
                        break;
                      case 'logout':
                        authService.logout();
                        break;
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
