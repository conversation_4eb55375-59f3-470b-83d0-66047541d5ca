import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';

import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/loading.dart';
import '../../../shared/widgets/toast.dart';
import '../services/users_service.dart';
import '../../../shared/models/user_model.dart';

class UserDetailsScreen extends StatefulWidget {
  final int? userId;
  final bool isNewUser;

  const UserDetailsScreen({super.key, this.userId, this.isNewUser = false});

  @override
  State<UserDetailsScreen> createState() => _UserDetailsScreenState();
}

class _UserDetailsScreenState extends State<UserDetailsScreen>
    with SingleTickerProviderStateMixin {
  final UsersService _usersService = UsersService();
  final _formKey = GlobalKey<FormState>();
  late TabController _tabController;

  User? _user;
  bool _isLoading = false;
  bool _isSaving = false;

  // Form fields
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  bool _isActive = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    if (!widget.isNewUser && widget.userId != null) {
      _loadUserDetails();
    }
  }

  Future<void> _loadUserDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = await _usersService.getUserDetails(widget.userId!);
      setState(() {
        _user = user;
        _isLoading = false;

        // Populate form fields
        _usernameController.text = user.username;
        _emailController.text = user.email ?? '';
        _fullNameController.text = user.fullName;
        _phoneController.text = user.phone ?? '';
        _addressController.text = user.address ?? '';
        _isActive = user.isActive;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ToastService.showError('Failed to load user details: ${e.toString()}');
    }
  }

  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final userData = {
        'username': _usernameController.text,
        'email': _emailController.text,
        'fullName': _fullNameController.text,
        'phone': _phoneController.text,
        'address': _addressController.text,
        'isActive': _isActive,
      };

      if (_passwordController.text.isNotEmpty) {
        userData['password'] = _passwordController.text;
      }

      if (widget.isNewUser) {
        await _usersService.createUser(userData);
        ToastService.showSuccess('users.create_success'.tr());
      } else {
        await _usersService.updateUser(widget.userId!, userData);
        ToastService.showSuccess('users.update_success'.tr());
      }

      setState(() {
        _isSaving = false;
      });

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      setState(() {
        _isSaving = false;
      });
      ToastService.showError(
        widget.isNewUser
            ? 'users.create_error'.tr(args: [e.toString()])
            : 'users.update_error'.tr(args: [e.toString()]),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isNewUser = widget.isNewUser;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isNewUser ? 'users.add_user'.tr() : 'users.user_details'.tr(),
        ),
        actions: [
          if (!isNewUser && !_isLoading && _user != null)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadUserDetails,
              tooltip: 'common.refresh'.tr(),
            ),
        ],
        bottom: !isNewUser && !_isLoading && _user != null
            ? TabBar(
                controller: _tabController,
                tabs: [
                  Tab(text: 'users.profile'.tr()),
                  Tab(text: 'users.traffic'.tr()),
                  Tab(text: 'users.sessions'.tr()),
                ],
              )
            : null,
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : isNewUser || _user != null
          ? TabBarView(
              controller: _tabController,
              physics: !isNewUser
                  ? const AlwaysScrollableScrollPhysics()
                  : const NeverScrollableScrollPhysics(),
              children: [
                _buildProfileTab(),
                if (!isNewUser) _buildTrafficTab() else Container(),
                if (!isNewUser) _buildSessionsTab() else Container(),
              ],
            )
          : Center(child: Text('users.user_not_found'.tr())),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: CustomButton(
          onPressed: _isSaving ? null : _saveUser,
          text: isNewUser ? 'users.create_user'.tr() : 'common.save'.tr(),
          isLoading: _isSaving,
          fullWidth: true,
        ),
      ),
    );
  }

  Widget _buildProfileTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'users.basic_info'.tr(),
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _usernameController,
                    label: 'users.username'.tr(),
                    prefix: const Icon(Icons.person),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'users.username_required'.tr();
                      }
                      if (value.length < 3) {
                        return 'users.username_too_short'.tr();
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _emailController,
                    label: 'users.email'.tr(),
                    prefix: const Icon(Icons.email),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'users.email_required'.tr();
                      }
                      if (!RegExp(
                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                      ).hasMatch(value)) {
                        return 'users.email_invalid'.tr();
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  PasswordTextField(
                    controller: _passwordController,
                    label: widget.isNewUser
                        ? 'users.password'.tr()
                        : 'users.new_password'.tr(),
                    prefix: const Icon(Icons.lock),
                    validator: widget.isNewUser
                        ? (value) {
                            if (value == null || value.isEmpty) {
                              return 'users.password_required'.tr();
                            }
                            if (value.length < 6) {
                              return 'users.password_too_short'.tr();
                            }
                            return null;
                          }
                        : null,
                    hint: !widget.isNewUser
                        ? 'users.password_leave_blank'.tr()
                        : null,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            CustomCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'users.personal_info'.tr(),
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _fullNameController,
                    label: 'users.full_name'.tr(),
                    prefix: const Icon(Icons.badge),
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _phoneController,
                    label: 'users.phone'.tr(),
                    prefix: const Icon(Icons.phone),
                    keyboardType: TextInputType.phone,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _addressController,
                    label: 'users.address'.tr(),
                    prefix: const Icon(Icons.location_on),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            CustomCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'users.account_status'.tr(),
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: Text('users.active_account'.tr()),
                    subtitle: Text('users.active_account_desc'.tr()),
                    value: _isActive,
                    onChanged: (value) {
                      setState(() {
                        _isActive = value;
                      });
                    },
                    secondary: Icon(
                      _isActive ? Icons.check_circle : Icons.cancel,
                      color: _isActive ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrafficTab() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _usersService.getUserTraffic(widget.userId!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: LoadingIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'users.traffic_error'.tr(args: [snapshot.error.toString()]),
            ),
          );
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(child: Text('users.no_traffic_data'.tr()));
        }

        final trafficData = snapshot.data!;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'users.traffic_summary'.tr(),
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildTrafficStat(
                            'users.download'.tr(),
                            '${(trafficData['download'] ?? 0) / 1024 / 1024 / 1024} GB',
                            Icons.download,
                            Colors.blue,
                          ),
                        ),
                        Expanded(
                          child: _buildTrafficStat(
                            'users.upload'.tr(),
                            '${(trafficData['upload'] ?? 0) / 1024 / 1024 / 1024} GB',
                            Icons.upload,
                            Colors.green,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildTrafficStat(
                            'users.total'.tr(),
                            '${((trafficData['download'] ?? 0) + (trafficData['upload'] ?? 0)) / 1024 / 1024 / 1024} GB',
                            Icons.data_usage,
                            Colors.purple,
                          ),
                        ),
                        Expanded(
                          child: _buildTrafficStat(
                            'users.quota'.tr(),
                            '${(trafficData['quota'] ?? 0) / 1024 / 1024 / 1024} GB',
                            Icons.storage,
                            Colors.orange,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              CustomCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'users.traffic_history'.tr(),
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    // Placeholder for traffic history chart
                    Container(
                      height: 200,
                      width: double.infinity,
                      color: Colors.grey.withValues(alpha: 0.1),
                      child: Center(
                        child: Text('users.traffic_chart_placeholder'.tr()),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTrafficStat(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(title, style: Theme.of(context).textTheme.titleSmall),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildSessionsTab() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _usersService.getUserSessions(widget.userId!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: LoadingIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'users.sessions_error'.tr(args: [snapshot.error.toString()]),
            ),
          );
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(child: Text('users.no_sessions'.tr()));
        }

        final sessions = snapshot.data!;

        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: sessions.length,
          itemBuilder: (context, index) {
            final session = sessions[index];
            final isActive = session['active'] ?? false;
            final startTime = DateTime.parse(
              session['startTime'] ?? DateTime.now().toIso8601String(),
            );
            final lastSeen = DateTime.parse(
              session['lastSeen'] ?? DateTime.now().toIso8601String(),
            );

            return CustomCard(
              margin: const EdgeInsets.only(bottom: 16),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: isActive
                      ? Colors.green.withValues(alpha: 0.2)
                      : Colors.grey.withValues(alpha: 0.2),
                  child: Icon(
                    Icons.devices,
                    color: isActive ? Colors.green : Colors.grey,
                  ),
                ),
                title: Text(session['deviceName'] ?? 'Unknown Device'),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('IP: ${session['ip'] ?? 'Unknown'}'),
                    Text(
                      'users.session_started'.tr(
                        args: [DateFormat.yMd().add_Hm().format(startTime)],
                      ),
                    ),
                  ],
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: isActive
                            ? Colors.green.withValues(alpha: 0.2)
                            : Colors.grey.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        isActive ? 'users.active'.tr() : 'users.inactive'.tr(),
                        style: TextStyle(
                          color: isActive ? Colors.green : Colors.grey,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'users.last_seen'.tr(
                        args: [DateFormat.Hm().format(lastSeen)],
                      ),
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                isThreeLine: true,
                onTap: () {
                  // Show session details or actions
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text('users.session_details'.tr()),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSessionDetail(
                            'users.device'.tr(),
                            session['deviceName'] ?? 'Unknown',
                          ),
                          _buildSessionDetail(
                            'users.ip'.tr(),
                            session['ip'] ?? 'Unknown',
                          ),
                          _buildSessionDetail(
                            'users.location'.tr(),
                            session['location'] ?? 'Unknown',
                          ),
                          _buildSessionDetail(
                            'users.browser'.tr(),
                            session['browser'] ?? 'Unknown',
                          ),
                          _buildSessionDetail(
                            'users.os'.tr(),
                            session['os'] ?? 'Unknown',
                          ),
                          _buildSessionDetail(
                            'users.started'.tr(),
                            DateFormat.yMd().add_Hm().format(startTime),
                          ),
                          _buildSessionDetail(
                            'users.last_activity'.tr(),
                            DateFormat.yMd().add_Hm().format(lastSeen),
                          ),
                        ],
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text('common.close'.tr()),
                        ),
                        if (isActive)
                          TextButton(
                            onPressed: () async {
                              Navigator.pop(context);
                              final sessionId =
                                  session['id'] ?? session['sessionId'];
                              if (sessionId == null) {
                                ToastService.showError(
                                  'users.session_id_not_found'.tr(),
                                );
                                return;
                              }
                              final success = await _usersService
                                  .terminateSession(sessionId.toString());
                              if (!mounted) return;
                              if (success) {
                                setState(() {
                                  // Mark the session as inactive locally so the UI updates immediately
                                  sessions[index]['active'] = false;
                                });
                                ToastService.showSuccess(
                                  'users.session_terminated'.tr(),
                                );
                              } else {
                                ToastService.showError(
                                  'users.session_terminate_failed'.tr(),
                                );
                              }
                            },
                            child: Text(
                              'users.terminate_session'.tr(),
                              style: const TextStyle(color: Colors.red),
                            ),
                          ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSessionDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.bold)),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _fullNameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }
}
