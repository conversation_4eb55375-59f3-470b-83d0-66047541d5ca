import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter/foundation.dart';

import '../../shared/constants/app_constants.dart';

class ApiService {
  late Dio _dio;
  final String? baseUrl;
  final String? token;

  ApiService({this.baseUrl, this.token}) {
    _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl ?? AppConstants.baseUrl, // Use proxy server
        connectTimeout: const Duration(
          milliseconds: AppConstants.connectionTimeout,
        ),
        receiveTimeout: const Duration(
          milliseconds: AppConstants.receiveTimeout,
        ),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      ),
    );

    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          if (kDebugMode) {
            debugPrint('REQUEST[${options.method}] => PATH: ${options.path}');
          }
          return handler.next(options);
        },
        onResponse: (response, handler) {
          if (kDebugMode) {
            debugPrint(
              'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}',
            );
          }
          return handler.next(response);
        },
        onError: (DioException e, handler) {
          if (kDebugMode) {
            debugPrint(
              'ERROR[${e.response?.statusCode}] => PATH: ${e.requestOptions.path}',
            );
          }
          return handler.next(e);
        },
      ),
    );
  }

  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      return response;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return response;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return response;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
      );
      return response;
    } on DioException catch (e) {
      return _handleError(e);
    }
  }

  /// Post with encrypted payload (similar to azawi_flutter)
  Future<Response> postWithPayload(
    String endpoint,
    Map<String, dynamic> data, {
    String? apiUrl,
  }) async {
    try {
      // Generate encrypted payload
      final encryptedPayload = generatePayload(data);

      // Prepare request data with api_domain in body (Laravel expects this)
      final requestData = {'payload': encryptedPayload};

      // Add api_domain to request body and header
      final headers = <String, dynamic>{};
      String targetDomain;

      if (apiUrl != null && apiUrl.isNotEmpty) {
        if (apiUrl != '***********') {
          targetDomain = apiUrl;
        } else {
          // Use localhost:8080 for mock API testing
          targetDomain = 'http://localhost:8080/api';
        }
      } else {
        // Default to localhost:8080 for mock API testing
        targetDomain = 'http://localhost:8080/api';
      }

      // Add to both header and body for Laravel compatibility (Laravel expects full URL)
      headers['X-API-Domain'] = targetDomain;
      requestData['api_domain'] = targetDomain;

      // Use Laravel API directly for postWithPayload
      final dio = Dio(
        BaseOptions(
          baseUrl: 'http://127.0.0.1:8000', // Laravel API directly
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            if (token != null) 'Authorization': 'Bearer $token',
            ...headers,
          },
        ),
      );

      final response = await dio.post(endpoint, data: requestData);

      return response;
    } on DioException catch (e) {
      if (kDebugMode) {
        print('API Error: ${e.response?.statusCode} - ${e.response?.data}');
      }
      return _handleError(e);
    }
  }

  /// Generate encrypted payload (exactly like Angular CryptoJS.AES.encrypt)
  String generatePayload(Map<String, dynamic> data) {
    const encryptionKey = 'abcdefghijuklmno0123456789012345';
    final jsonString = json.encode(data);

    // Use the exact same approach as Angular CryptoJS.AES.encrypt
    // CryptoJS uses PBKDF2 with salt for key derivation
    final key = encrypt.Key.fromUtf8(encryptionKey);
    final encrypter = encrypt.Encrypter(
      encrypt.AES(key, mode: encrypt.AESMode.cbc),
    );

    // Generate random IV (16 bytes for AES)
    final iv = encrypt.IV.fromSecureRandom(16);

    final encrypted = encrypter.encrypt(jsonString, iv: iv);

    // Format exactly like CryptoJS: base64(salt + encrypted_data)
    // CryptoJS format: "Salted__" + salt + encrypted_data
    const saltedPrefix = 'Salted__';
    final salt = iv.bytes.sublist(0, 8); // Use first 8 bytes of IV as salt
    final combined = saltedPrefix.codeUnits + salt + encrypted.bytes;

    return base64.encode(combined);
  }

  Future<Response> _handleError(DioException e) {
    String errorMessage = '';

    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
        errorMessage = AppConstants.connectionErrorMessage;
        break;
      case DioExceptionType.badResponse:
        switch (e.response?.statusCode) {
          case 400:
            errorMessage = e.response?.data['message'] ?? 'Bad request';
            break;
          case 401:
            errorMessage = AppConstants.unauthorizedErrorMessage;
            break;
          case 404:
            errorMessage = AppConstants.notFoundErrorMessage;
            break;
          case 500:
          case 501:
          case 502:
          case 503:
            errorMessage = AppConstants.serverErrorMessage;
            break;
          default:
            errorMessage =
                'Error occurred with status code: ${e.response?.statusCode}';
        }
        break;
      case DioExceptionType.cancel:
        errorMessage = 'Request was cancelled';
        break;
      case DioExceptionType.unknown:
      default:
        errorMessage = 'Unexpected error occurred';
    }

    return Future.error(errorMessage);
  }

  void updateToken(String newToken) {
    _dio.options.headers['Authorization'] = 'Bearer $newToken';
  }

  void updateBaseUrl(String newBaseUrl) {
    _dio.options.baseUrl = newBaseUrl;
  }
}
