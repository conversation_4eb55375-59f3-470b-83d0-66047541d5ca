class AppConstants {
  // API URLs
  static const String baseUrl = 'http://127.0.0.1:8000'; // Laravel API directly
  static const String apiUrl = '$baseUrl/api';

  // API Endpoints
  static const String loginEndpoint = 'api/auth/login';
  static const String logoutEndpoint = 'api/auth/logout';
  static const String usersEndpoint = 'api/users';
  static const String onlineUsersEndpoint = 'api/users/online';
  static const String userDetailsEndpoint = 'api/users/';
  static const String userTrafficEndpoint = 'api/users/traffic/';
  static const String userSessionsEndpoint = 'api/users/sessions/';
  // Endpoint to terminate a user session (expects session id appended)
  static const String terminateSessionEndpoint =
      'api/users/sessions/terminate/';
  static const String userInvoicesEndpoint = 'api/users/invoices/';

  // Local Storage Keys
  static const String tokenKey = 'token';
  static const String userKey = 'user';
  static const String domainKey = 'domain';
  static const String rememberMeKey = 'rememberMe';
  static const String themeKey = 'theme';
  static const String languageKey = 'language';

  // App Settings
  static const String appName = 'Abraji Internet';
  static const String appVersion = '1.0.0';
  static const List<String> supportedLanguages = ['en', 'ar'];
  static const String defaultLanguage = 'en';

  // Pagination
  static const int defaultPageSize = 10;
  static const int maxPageSize = 100;

  // Timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  // Error Messages
  static const String connectionErrorMessage =
      'Connection error. Please check your internet connection.';
  static const String serverErrorMessage =
      'Server error. Please try again later.';
  static const String unauthorizedErrorMessage =
      'Unauthorized. Please login again.';
  static const String notFoundErrorMessage = 'Resource not found.';

  // Routes
  static const String loginRoute = '/login';
  static const String dashboardRoute = '/';
  static const String usersRoute = '/users';
  static const String userDetailsRoute = '/users/:id';
  static const String settingsRoute = '/settings';
  static const String profileRoute = '/profile';
}
