import '../../../../core/error/exceptions.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../../../shared/constants/app_constants.dart';
import '../models/auth_response_model.dart';
import '../../../../shared/models/user_model.dart' as shared_user;
import '../models/user_model.dart';

/// Authentication Local Data Source Interface
abstract class AuthLocalDataSource {
  Future<AuthResponseModel?> getCachedAuthData();
  Future<void> cacheAuthData(AuthResponseModel authResponse);
  Future<void> clearAuthData();
  Future<UserModel?> getCachedUser();
  Future<void> cacheUser(UserModel user);
  Future<String?> getCachedToken();
  Future<void> cacheToken(String token);
  Future<String?> getCachedDomain();
  Future<void> cacheDomain(String domain);
  Future<bool> getRememberMe();
  Future<void> setRememberMe(bool rememberMe);
  Future<bool> isAuthenticated();
}

/// Authentication Local Data Source Implementation
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final LocalStorageService localStorageService;

  AuthLocalDataSourceImpl({required this.localStorageService});

  @override
  Future<AuthResponseModel?> getCachedAuthData() async {
    try {
      final token = await localStorageService.getToken();
      final userJson = await localStorageService.getUser();

      if (token != null && userJson != null) {
        return AuthResponseModel(
          token: token,
          user: _toUserModel(
            shared_user.User(
              id: userJson.id,
              username: userJson.username,
              email: userJson.email ?? '',
              firstname: userJson.firstname,
              lastname: userJson.lastname,
              parentId: userJson.parentId,
              simultaneousSessions: userJson.simultaneousSessions,
              nRow: userJson.nRow,
              remainingDays: userJson.remainingDays,
              status: userJson.status,
              onlineStatus: userJson.onlineStatus,
            ),
          ),
        );
      }
      return null;
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached auth data: $e',
        code: 'CACHE_GET_ERROR',
      );
    }
  }

  @override
  Future<void> cacheAuthData(AuthResponseModel authResponse) async {
    try {
      await localStorageService.setToken(authResponse.token);
      await localStorageService.setUser(_toSharedUser(authResponse.user));
    } catch (e) {
      throw CacheException(
        message: 'Failed to cache auth data: $e',
        code: 'CACHE_SET_ERROR',
      );
    }
  }

  @override
  Future<void> clearAuthData() async {
    try {
      await localStorageService.removeToken();
      await localStorageService.removeUser();
      await localStorageService.removeCredentials();

      // Keep domain and remember me preference if remember me is enabled
      final rememberMe = await localStorageService.getRememberMe();
      if (!rememberMe) {
        await localStorageService.removeByKey(AppConstants.domainKey);
        await localStorageService.removeByKey(AppConstants.rememberMeKey);
      }
    } catch (e) {
      throw CacheException(
        message: 'Failed to clear auth data: $e',
        code: 'CACHE_CLEAR_ERROR',
      );
    }
  }

  @override
  Future<UserModel?> getCachedUser() async {
    try {
      final user = await localStorageService.getUser();
      if (user != null) {
        // Convert shared_user.User to UserModel using its JSON representation
        return UserModel.fromJson(user.toJson());
      }
      return null;
    } catch (e) {
      throw CacheException(message: 'Failed to get cached user: $e');
    }
  }

  @override
  Future<void> cacheUser(UserModel user) async {
    try {
      await localStorageService.setUser(_toSharedUser(user));
    } catch (e) {
      throw CacheException(message: 'Failed to cache user: $e');
    }
  }

  @override
  Future<String?> getCachedToken() async {
    try {
      return await localStorageService.getToken();
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached token: $e',
        code: 'CACHE_GET_TOKEN_ERROR',
      );
    }
  }

  @override
  Future<void> cacheToken(String token) async {
    try {
      await localStorageService.setToken(token);
    } catch (e) {
      throw CacheException(
        message: 'Failed to cache token: $e',
        code: 'CACHE_SET_TOKEN_ERROR',
      );
    }
  }

  @override
  Future<String?> getCachedDomain() async {
    try {
      return await localStorageService.getExternalDomain() ??
          await localStorageService.getDomain();
    } catch (e) {
      throw CacheException(
        message: 'Failed to get cached domain: $e',
        code: 'CACHE_GET_DOMAIN_ERROR',
      );
    }
  }

  @override
  Future<void> cacheDomain(String domain) async {
    try {
      await localStorageService.setDomain(domain);
      await localStorageService.setExternalDomain(domain);
    } catch (e) {
      throw CacheException(
        message: 'Failed to cache domain: $e',
        code: 'CACHE_SET_DOMAIN_ERROR',
      );
    }
  }

  @override
  Future<bool> getRememberMe() async {
    try {
      return await localStorageService.getRememberMe();
    } catch (e) {
      throw CacheException(
        message: 'Failed to get remember me preference: $e',
        code: 'CACHE_GET_REMEMBER_ME_ERROR',
      );
    }
  }

  @override
  Future<void> setRememberMe(bool rememberMe) async {
    try {
      await localStorageService.setRememberMe(rememberMe);
    } catch (e) {
      throw CacheException(
        message: 'Failed to set remember me preference: $e',
        code: 'CACHE_SET_REMEMBER_ME_ERROR',
      );
    }
  }

  // Helper method to map UserModel to shared_user.User
  shared_user.User _toSharedUser(dynamic user) {
    if (user is UserModel) {
      return shared_user.User(
        id: int.tryParse(user.id) ?? 0,
        username: user.username,
        email: user.email ?? '',
        firstname: user.name?.split(' ').first,
        lastname: user.name?.split(' ').skip(1).join(' '),
        parentId: 0,
        simultaneousSessions: 1,
        nRow: 1,
        remainingDays: 365,
        status: 'active',
        onlineStatus: 1,
      );
    } else {
      // Handle domain User entity
      return shared_user.User(
        id: int.tryParse(user.id) ?? 0,
        username: user.username,
        email: user.email ?? '',
        firstname: user.name?.split(' ').first,
        lastname: user.name?.split(' ').skip(1).join(' '),
        parentId: 0,
        simultaneousSessions: 1,
        nRow: 1,
        remainingDays: 365,
        status: 'active',
        onlineStatus: 1,
      );
    }
  }

  // Helper method to convert shared_user.User to UserModel
  UserModel _toUserModel(shared_user.User user) {
    return UserModel(
      id: user.id.toString(),
      username: user.username,
      email: user.email ?? '',
      name: user.fullName,
    );
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      final token = await localStorageService.getToken();
      if (token == null) return false;

      // Additional validation can be added here
      // For example, checking token expiry
      return true;
    } catch (e) {
      return false;
    }
  }
}
