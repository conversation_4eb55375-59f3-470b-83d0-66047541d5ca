import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../constants/app_constants.dart';
import 'encryption_service.dart';

class ApiService {
  late final Dio _dio;
  final String baseUrl;

  ApiService({String? baseUrl}) : baseUrl = baseUrl ?? AppConstants.baseUrl {
    _dio = Dio();
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          if (kDebugMode) {
            debugPrint('🌐 API Request: ${options.method} ${options.uri}');
            debugPrint('📤 Request Data: ${options.data}');
          }
          handler.next(options);
        },
        onResponse: (response, handler) {
          if (kDebugMode) {
            debugPrint('✅ API Response: ${response.statusCode} ${response.requestOptions.uri}');
            debugPrint('📥 Response Data: ${response.data}');
          }
          handler.next(response);
        },
        onError: (error, handler) {
          if (kDebugMode) {
            debugPrint('❌ API Error: ${error.response?.statusCode} ${error.requestOptions.uri}');
            debugPrint('📥 Error Data: ${error.response?.data}');
          }
          handler.next(error);
        },
      ),
    );
  }

  /// Post with encrypted payload (similar to azawi_flutter)
  Future<Response> postWithPayload(
    String endpoint,
    Map<String, dynamic> data, {
    String? apiUrl,
  }) async {
    try {
      // Generate encrypted payload
      final payload = EncryptionService.generatePayload(data);

      // Prepare request data with api_domain in body
      final requestData = {
        'payload': payload,
        if (apiUrl != null) 'api_domain': apiUrl,
      };

      final response = await _dio.post(
        '$baseUrl/$endpoint',
        data: requestData,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      return response;
    } on DioException catch (e) {
      if (kDebugMode) {
        debugPrint('API Error: ${e.response?.statusCode} - ${e.response?.data}');
      }
      rethrow;
    }
  }

  /// Standard POST method
  Future<Response> post(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.post(
        '$baseUrl/$endpoint',
        data: data,
        queryParameters: queryParameters,
      );
      return response;
    } on DioException catch (e) {
      rethrow;
    }
  }

  /// Standard GET method
  Future<Response> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.get(
        '$baseUrl/$endpoint',
        queryParameters: queryParameters,
      );
      return response;
    } on DioException catch (e) {
      rethrow;
    }
  }

  /// Standard PUT method
  Future<Response> put(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.put(
        '$baseUrl/$endpoint',
        data: data,
        queryParameters: queryParameters,
      );
      return response;
    } on DioException catch (e) {
      rethrow;
    }
  }

  /// Standard DELETE method
  Future<Response> delete(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.delete(
        '$baseUrl/$endpoint',
        queryParameters: queryParameters,
      );
      return response;
    } on DioException catch (e) {
      rethrow;
    }
  }
}
