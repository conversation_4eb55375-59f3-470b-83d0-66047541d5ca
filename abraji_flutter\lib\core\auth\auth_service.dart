import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

import '../../shared/constants/app_constants.dart';
import '../../shared/models/user_model.dart';
import '../services/encryption_service.dart';
import '../services/local_storage_service.dart';

class AuthService with ChangeNotifier {
  final Dio _dio = Dio();

  final LocalStorageService _localStorageService = LocalStorageService();
  bool _isAuthenticated = false;
  User? _currentUser;
  String? _token;
  String? _domain;
  Timer? _tokenCheckTimer;

  bool get isAuthenticated {
    if (_token == null) return false;

    try {
      // Check if token is expired (similar to Angular implementation)
      final bool isExpired = JwtDecoder.isExpired(_token!);
      if (isExpired) {
        _isAuthenticated = false;
        _token = null;
        _currentUser = null;
        notifyListeners();
        return false;
      }
      return _isAuthenticated;
    } catch (e) {
      debugPrint('Token validation error: $e');
      _isAuthenticated = false;
      return false;
    }
  }

  User? get currentUser => _currentUser;
  String? get token => _token;
  String? get domain => _domain;

  /// Get user ID from token (similar to Angular's getAuthId)
  String? getUserId() {
    if (!isAuthenticated || _token == null) {
      return null;
    }

    try {
      final Map<String, dynamic> decodedToken = JwtDecoder.decode(_token!);
      return decodedToken['sub']?.toString();
    } catch (e) {
      debugPrint('Error decoding token for user ID: $e');
      return null;
    }
  }

  AuthService() {
    _init();
  }

  Future<void> _init() async {
    await loadUserFromStorage();
  }

  Future<void> loadUserFromStorage() async {
    final savedToken = await _localStorageService.getToken();
    final savedUser = await _localStorageService.getUser();
    final savedDomain = await _localStorageService.getDomain();

    if (savedToken != null && savedUser != null) {
      try {
        // Validate token before setting authentication state
        final bool isExpired = JwtDecoder.isExpired(savedToken);
        if (!isExpired) {
          _token = savedToken;
          _domain = savedDomain;
          _currentUser = savedUser;
          _isAuthenticated = true;
          _setupDioInterceptors();
          _startTokenCheckTimer();
        } else {
          // Clear expired token
          await _localStorageService.removeToken();
          await _localStorageService.removeUser();
        }
      } catch (e) {
        debugPrint('Token validation error during load: $e');
        // Clear invalid token
        await _localStorageService.removeToken();
        await _localStorageService.removeUser();
      }
      notifyListeners();
    }
  }

  void _setupDioInterceptors() {
    _dio.interceptors.clear();
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          if (_token != null) {
            options.headers['Authorization'] = 'Bearer $_token';
          }
          return handler.next(options);
        },
        onError: (DioException error, handler) {
          if (error.response?.statusCode == 401) {
            logout();
          }
          return handler.next(error);
        },
      ),
    );
  }

  Future<bool> login(
    String username,
    String password,
    String domain,
    bool rememberMe,
  ) async {
    try {
      // Always use the proxy server for API calls
      const baseUrl =
          AppConstants.baseUrl; // This should be http://127.0.0.1:8081

      // Prepare and encrypt request data similar to Angular implementation
      final loginData = {'username': username, 'password': password};

      final encryptedPayload = EncryptionService.generatePayload(loginData);

      final Map<String, dynamic> requestData = {'payload': encryptedPayload};

      // Add domain to request data if provided (similar to Angular api_domain)
      // Use localhost:8080 for mock API testing if no domain provided or if domain is the problematic external one
      if (domain.isNotEmpty && domain != '***********') {
        requestData['api_domain'] = domain;
      } else {
        // Use localhost:8080 for mock API testing
        requestData['api_domain'] = 'http://localhost:8080/api';
      }

      final response = await _dio.post(
        '$baseUrl${AppConstants.loginEndpoint}',
        data: requestData,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        _token = data['token'];
        _domain = domain; // Store the original domain for reference

        // Handle user data - check if it exists in response
        if (data['user'] != null) {
          _currentUser = User.fromJson(data['user']);
        } else {
          // Create a default user if not provided
          _currentUser = User(
            id: 1234,
            username: 'admin',
            email: '<EMAIL>',
            firstname: 'Admin',
            lastname: 'User',
            parentId: 0,
            simultaneousSessions: 1,
            nRow: 1,
            remainingDays: 365,
            status: 'active',
            onlineStatus: 1,
          );
        }

        _isAuthenticated = true;
        _setupDioInterceptors();
        _startTokenCheckTimer();

        // Always save token and user data for session persistence
        await _localStorageService.setToken(_token!);
        await _localStorageService.setUser(_currentUser!);
        await _localStorageService.setDomain(_domain ?? '');
        await _localStorageService.setRememberMe(rememberMe);

        // Save external domain for future use
        if (domain.isNotEmpty) {
          await _localStorageService.setExternalDomain(domain);
        }

        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Login error: $e');
      return false;
    }
  }

  Future<void> logout() async {
    try {
      if (_token != null) {
        // Always use proxy server for logout
        final headers = {'Authorization': 'Bearer $_token'};

        // Add domain header with fallback logic
        if (_domain != null &&
            _domain!.isNotEmpty &&
            _domain != '***********') {
          headers['x-api-domain'] = _domain!;
        } else {
          // Use local Laravel server as fallback
          headers['x-api-domain'] = '127.0.0.1:8000';
        }

        await _dio.post(
          '${AppConstants.baseUrl}${AppConstants.logoutEndpoint}',
          options: Options(headers: headers),
        );
      }
    } catch (e) {
      debugPrint('Logout error: $e');
    } finally {
      _stopTokenCheckTimer();
      _token = null;
      _currentUser = null;
      _isAuthenticated = false;

      await _localStorageService.removeToken();
      await _localStorageService.removeUser();
      await _localStorageService.removeCredentials();

      // Keep domain and remember me preference
      final rememberMe = await _localStorageService.getRememberMe();
      if (!rememberMe) {
        await _localStorageService.removeByKey(AppConstants.domainKey);
        await _localStorageService.removeByKey(AppConstants.rememberMeKey);
      }

      notifyListeners();
    }
  }

  Future<String?> getSavedDomain() async {
    return await _localStorageService.getExternalDomain() ??
        await _localStorageService.getDomain();
  }

  Future<bool> getSavedRememberMe() async {
    return await _localStorageService.getRememberMe();
  }

  Future<void> saveDomain(String domain) async {
    await _localStorageService.setDomain(domain);
    await _localStorageService.setExternalDomain(domain);
    _domain = domain;
  }

  /// Start periodic token validation check
  void _startTokenCheckTimer() {
    _tokenCheckTimer?.cancel();

    // Check token every 5 minutes
    _tokenCheckTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (_token != null) {
        try {
          final bool isExpired = JwtDecoder.isExpired(_token!);
          if (isExpired) {
            debugPrint('Token expired, logging out user');
            logout();
            timer.cancel();
          } else {
            // Check if token will expire in the next 30 minutes
            final Map<String, dynamic> decodedToken = JwtDecoder.decode(
              _token!,
            );
            final int exp = decodedToken['exp'];
            final DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(
              exp * 1000,
            );
            final DateTime now = DateTime.now();
            final Duration timeUntilExpiry = expiryDate.difference(now);

            if (timeUntilExpiry.inMinutes <= 30) {
              debugPrint(
                'Token will expire soon: ${timeUntilExpiry.inMinutes} minutes remaining',
              );
              // Here you could implement token refresh logic if your API supports it
            }
          }
        } catch (e) {
          debugPrint('Token check error: $e');
          logout();
          timer.cancel();
        }
      } else {
        timer.cancel();
      }
    });
  }

  /// Stop token check timer
  void _stopTokenCheckTimer() {
    _tokenCheckTimer?.cancel();
    _tokenCheckTimer = null;
  }

  @override
  void dispose() {
    _stopTokenCheckTimer();
    super.dispose();
  }
}
