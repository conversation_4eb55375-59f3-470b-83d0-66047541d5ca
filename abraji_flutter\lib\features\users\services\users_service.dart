import 'package:flutter/foundation.dart';
import '../../../core/network/api_service.dart';
import '../../../shared/constants/app_constants.dart';
import '../../../shared/models/user_model.dart';
import '../../../core/auth/auth_service.dart';
import '../../../core/di/injection_container.dart' as di;

class UsersService {
  final ApiService _apiService;

  UsersService([ApiService? apiService])
    : _apiService = apiService ?? ApiService();

  Future<List<User>> getAllUsers({
    int page = 1,
    int pageSize = AppConstants.defaultPageSize,
    String? search,
  }) async {
    try {
      // Prepare request data similar to Angular UserForm
      final requestData = {
        'page': page,
        'page_size': pageSize,
        'search': search ?? '',
        'sort_by': 'id',
        'sort_direction': 'desc',
      };

      // Get auth service for token
      final authService = di.sl<AuthService>();
      final token = authService.token;

      // Create API service with token
      final apiServiceWithToken = ApiService(token: token);

      // Use POST with encrypted payload (Laravel API expects this)
      final response = await apiServiceWithToken.postWithPayload(
        '/api/users/table', // Laravel endpoint
        requestData,
      );

      // Handle response structure similar to Angular TableResponse
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        final List<dynamic> usersData = responseData['data'] ?? [];
        return usersData.map((userData) => User.fromJson(userData)).toList();
      } else {
        return [];
      }
    } catch (e) {
      debugPrint('UsersService.getAllUsers error: $e');
      rethrow;
    }
  }

  Future<List<User>> getOnlineUsers({
    int page = 1,
    int pageSize = AppConstants.defaultPageSize,
  }) async {
    try {
      // Prepare request data similar to Angular UserForm
      final requestData = {
        'page': page,
        'page_size': pageSize,
        'search': '',
        'sort_by': 'id',
        'sort_direction': 'desc',
      };

      // Get auth service for token
      final authService = di.sl<AuthService>();
      final token = authService.token;

      // Create API service with token
      final apiServiceWithToken = ApiService(token: token);

      // Use POST with encrypted payload (Laravel API expects this)
      final response = await apiServiceWithToken.postWithPayload(
        '/api/users/online', // Laravel endpoint
        requestData,
      );

      // Handle response structure similar to Angular TableResponse
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        final List<dynamic> usersData = responseData['data'] ?? [];
        return usersData.map((userData) => User.fromJson(userData)).toList();
      } else {
        return [];
      }
    } catch (e) {
      debugPrint('UsersService.getOnlineUsers error: $e');
      rethrow;
    }
  }

  Future<User> getUserDetails(int userId) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.userDetailsEndpoint}$userId',
      );
      return User.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }

  Future<User> createUser(Map<String, dynamic> userData) async {
    try {
      final response = await _apiService.post(
        AppConstants.usersEndpoint,
        data: userData,
      );
      return User.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }

  Future<User> updateUser(int userId, Map<String, dynamic> userData) async {
    try {
      final response = await _apiService.put(
        '${AppConstants.userDetailsEndpoint}$userId',
        data: userData,
      );
      return User.fromJson(response.data);
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> deleteUser(int userId) async {
    try {
      await _apiService.delete('${AppConstants.userDetailsEndpoint}$userId');
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<Map<String, dynamic>> getUserTraffic(
    int userId, {
    String? period,
  }) async {
    try {
      final queryParams = period != null ? {'period': period} : null;
      final response = await _apiService.get(
        '${AppConstants.userTrafficEndpoint}$userId',
        queryParameters: queryParams,
      );
      return response.data;
    } catch (e) {
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getUserSessions(int userId) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.userSessionsEndpoint}$userId',
      );
      final List<dynamic> sessionsData = response.data['sessions'] ?? [];
      return sessionsData.cast<Map<String, dynamic>>();
    } catch (e) {
      rethrow;
    }
  }

  /// Terminates a specific user session by its `sessionId`.
  /// Returns `true` if the termination request succeeds.
  Future<bool> terminateSession(String sessionId) async {
    try {
      await _apiService.post(
        '${AppConstants.terminateSessionEndpoint}$sessionId',
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<List<dynamic>> getUserInvoices(int userId) async {
    try {
      final response = await _apiService.get(
        '${AppConstants.userInvoicesEndpoint}$userId',
      );
      return response.data['invoices'] ?? [];
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> activateUser(int userId) async {
    try {
      await _apiService.post(
        '${AppConstants.userDetailsEndpoint}$userId/activate',
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> sendMessage(int userId, String message) async {
    try {
      await _apiService.post(
        '${AppConstants.userDetailsEndpoint}$userId/message',
        data: {'message': message},
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> deactivateUser(int userId) async {
    try {
      await _apiService.post(
        '${AppConstants.userDetailsEndpoint}$userId/deactivate',
      );
      return true;
    } catch (e) {
      return false;
    }
  }
}
