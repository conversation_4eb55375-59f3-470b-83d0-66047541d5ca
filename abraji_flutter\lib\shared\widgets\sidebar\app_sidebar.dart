import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';

import '../../../core/auth/auth_service.dart';
import '../../../core/di/injection_container.dart' as di;

class AppSidebar extends StatefulWidget {
  final VoidCallback toggleTheme;

  const AppSidebar({
    super.key,
    required this.toggleTheme,
  });

  @override
  State<AppSidebar> createState() => _AppSidebarState();
}

class _AppSidebarState extends State<AppSidebar> {
  bool _usersExpanded = false;
  bool _cardsExpanded = false;
  bool _invoicesExpanded = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currentRoute = GoRouterState.of(context).uri.path;

    return Container(
      width: 256,
      height: double.infinity,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: theme.dividerColor,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Sidebar content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.only(top: 80, left: 12, right: 12, bottom: 20),
              child: Column(
                children: [
                  // Dashboard
                  _buildMenuItem(
                    icon: Icons.dashboard_outlined,
                    title: 'sidebar.dashboard'.tr(),
                    route: '/',
                    currentRoute: currentRoute,
                  ),

                  const SizedBox(height: 8),

                  // Users section
                  _buildExpandableMenuItem(
                    icon: Icons.people_outline,
                    title: 'sidebar.users'.tr(),
                    isExpanded: _usersExpanded,
                    onToggle: () {
                      setState(() {
                        _usersExpanded = !_usersExpanded;
                      });
                    },
                    children: [
                      _buildSubMenuItem(
                        title: 'sidebar.usersList'.tr(),
                        route: '/users',
                        currentRoute: currentRoute,
                      ),
                      _buildSubMenuItem(
                        title: 'sidebar.onlineUsers'.tr(),
                        route: '/users/online',
                        currentRoute: currentRoute,
                      ),
                      _buildSubMenuItem(
                        title: 'sidebar.createUser'.tr(),
                        route: '/users/new',
                        currentRoute: currentRoute,
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Cards section
                  _buildExpandableMenuItem(
                    icon: Icons.credit_card_outlined,
                    title: 'sidebar.cards'.tr(),
                    isExpanded: _cardsExpanded,
                    onToggle: () {
                      setState(() {
                        _cardsExpanded = !_cardsExpanded;
                      });
                    },
                    children: [
                      _buildSubMenuItem(
                        title: 'sidebar.allCards'.tr(),
                        route: '/cards',
                        currentRoute: currentRoute,
                      ),
                      _buildSubMenuItem(
                        title: 'sidebar.createCard'.tr(),
                        route: '/cards/new',
                        currentRoute: currentRoute,
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Invoices section
                  _buildExpandableMenuItem(
                    icon: Icons.receipt_long_outlined,
                    title: 'sidebar.invoices'.tr(),
                    isExpanded: _invoicesExpanded,
                    onToggle: () {
                      setState(() {
                        _invoicesExpanded = !_invoicesExpanded;
                      });
                    },
                    children: [
                      _buildSubMenuItem(
                        title: 'sidebar.allInvoices'.tr(),
                        route: '/invoices',
                        currentRoute: currentRoute,
                      ),
                      _buildSubMenuItem(
                        title: 'sidebar.createInvoice'.tr(),
                        route: '/invoices/new',
                        currentRoute: currentRoute,
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Settings
                  _buildMenuItem(
                    icon: Icons.settings_outlined,
                    title: 'sidebar.settings'.tr(),
                    route: '/settings',
                    currentRoute: currentRoute,
                  ),
                ],
              ),
            ),
          ),

          // Bottom actions
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: theme.dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Language toggle
                IconButton(
                  icon: const Icon(Icons.language),
                  tooltip: 'common.language'.tr(),
                  onPressed: () {
                    final isArabic = context.locale.languageCode == 'ar';
                    final newLocale = isArabic 
                        ? const Locale('en') 
                        : const Locale('ar');
                    context.setLocale(newLocale);
                  },
                ),

                // Theme toggle
                IconButton(
                  icon: Icon(
                    theme.brightness == Brightness.dark
                        ? Icons.light_mode
                        : Icons.dark_mode,
                  ),
                  tooltip: 'common.theme'.tr(),
                  onPressed: widget.toggleTheme,
                ),

                // Logout
                IconButton(
                  icon: const Icon(Icons.logout, color: Colors.red),
                  tooltip: 'common.logout'.tr(),
                  onPressed: () {
                    final authService = di.sl<AuthService>();
                    authService.logout();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String route,
    required String currentRoute,
  }) {
    final theme = Theme.of(context);
    final isActive = currentRoute == route;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () => context.go(route),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: isActive ? theme.colorScheme.primary.withOpacity(0.1) : null,
              border: isActive
                  ? Border(
                      left: BorderSide(
                        color: theme.colorScheme.primary,
                        width: 3,
                      ),
                    )
                  : null,
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  size: 20,
                  color: isActive
                      ? theme.colorScheme.primary
                      : theme.textTheme.bodyMedium?.color,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: isActive
                          ? theme.colorScheme.primary
                          : theme.textTheme.bodyMedium?.color,
                      fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildExpandableMenuItem({
    required IconData icon,
    required String title,
    required bool isExpanded,
    required VoidCallback onToggle,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 2),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(8),
              onTap: onToggle,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      icon,
                      size: 20,
                      color: theme.textTheme.bodyMedium?.color,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        title,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),
                    Icon(
                      isExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      size: 20,
                      color: theme.textTheme.bodyMedium?.color,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        if (isExpanded) ...children,
      ],
    );
  }

  Widget _buildSubMenuItem({
    required String title,
    required String route,
    required String currentRoute,
  }) {
    final theme = Theme.of(context);
    final isActive = currentRoute == route;

    return Container(
      margin: const EdgeInsets.only(left: 32, top: 2, bottom: 2),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () => context.go(route),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: isActive ? theme.colorScheme.primary.withOpacity(0.1) : null,
              border: isActive
                  ? Border(
                      left: BorderSide(
                        color: theme.colorScheme.primary,
                        width: 3,
                      ),
                    )
                  : null,
            ),
            child: Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: isActive
                    ? theme.colorScheme.primary
                    : theme.textTheme.bodyMedium?.color,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
