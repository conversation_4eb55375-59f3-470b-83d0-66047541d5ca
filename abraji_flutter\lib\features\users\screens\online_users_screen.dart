import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../shared/widgets/custom_card.dart';
import '../../../shared/widgets/loading.dart';
import '../../../shared/widgets/toast.dart';
import '../services/users_service.dart';
import '../../../shared/models/user_model.dart';
import '../../../core/di/injection_container.dart' as di;
import '../../../core/network/api_service.dart';
import 'user_details_screen.dart';

class OnlineUsersScreen extends StatefulWidget {
  const OnlineUsersScreen({super.key});

  @override
  State<OnlineUsersScreen> createState() => _OnlineUsersScreenState();
}

class _OnlineUsersScreenState extends State<OnlineUsersScreen> {
  final UsersService _usersService = UsersService(di.sl<ApiService>());
  List<User> _onlineUsers = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadOnlineUsers();
    // Set up a timer to refresh online users every minute
    _setupRefreshTimer();
  }

  void _setupRefreshTimer() {
    Future.delayed(const Duration(minutes: 1), () {
      if (mounted) {
        _loadOnlineUsers();
        _setupRefreshTimer();
      }
    });
  }

  Future<void> _loadOnlineUsers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final users = await _usersService.getOnlineUsers();
      setState(() {
        _onlineUsers = users;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ToastService.showError('Failed to load online users: ${e.toString()}');
    }
  }

  List<User> get _filteredUsers {
    if (_searchQuery.isEmpty) {
      return _onlineUsers;
    }

    final query = _searchQuery.toLowerCase();
    return _onlineUsers.where((user) {
      return user.username.toLowerCase().contains(query) ||
          (user.email?.toLowerCase().contains(query) ?? false) ||
          user.fullName.toLowerCase().contains(query);
    }).toList();
  }

  void _viewUserDetails(User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserDetailsScreen(userId: user.id),
      ),
    ).then((_) => _loadOnlineUsers());
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'users.search'.tr(),
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Text(
                  'users.online_count'.tr(
                    args: [_onlineUsers.length.toString()],
                  ),
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (!_isLoading)
                  TextButton.icon(
                    onPressed: _loadOnlineUsers,
                    icon: const Icon(Icons.refresh),
                    label: Text('common.refresh'.tr()),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: _isLoading
                ? const Center(child: LoadingIndicator())
                : _filteredUsers.isEmpty
                ? Center(
                    child: Text(
                      _searchQuery.isEmpty
                          ? 'users.no_online_users'.tr()
                          : 'users.no_search_results'.tr(),
                      style: theme.textTheme.titleMedium,
                    ),
                  )
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: CustomCard(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: DataTable(
                          columns: [
                            DataColumn(label: Text('users.username'.tr())),
                            DataColumn(label: Text('users.email'.tr())),
                            DataColumn(label: Text('users.full_name'.tr())),
                            DataColumn(label: Text('users.last_login'.tr())),
                            DataColumn(
                              label: Text('users.session_duration'.tr()),
                            ),
                            DataColumn(label: Text('common.actions'.tr())),
                          ],
                          rows: _filteredUsers.map((user) {
                            // Calculate session duration
                            final now = DateTime.now();
                            final lastLoginStr = user.lastLogin;
                            final lastLogin = lastLoginStr != null
                                ? DateTime.parse(lastLoginStr)
                                : now;
                            final duration = now.difference(lastLogin);

                            String durationText;
                            if (duration.inHours > 0) {
                              durationText =
                                  '${duration.inHours}h ${duration.inMinutes % 60}m';
                            } else if (duration.inMinutes > 0) {
                              durationText =
                                  '${duration.inMinutes}m ${duration.inSeconds % 60}s';
                            } else {
                              durationText = '${duration.inSeconds}s';
                            }

                            return DataRow(
                              cells: [
                                DataCell(
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                        width: 10,
                                        height: 10,
                                        decoration: const BoxDecoration(
                                          color: Colors.green,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(user.username),
                                    ],
                                  ),
                                ),
                                DataCell(Text(user.email ?? '')),
                                DataCell(Text(user.fullName)),
                                DataCell(
                                  Text(
                                    user.lastLogin != null
                                        ? DateFormat.yMd().add_Hm().format(
                                            DateTime.parse(user.lastLogin!),
                                          )
                                        : '-',
                                  ),
                                ),
                                DataCell(Text(durationText)),
                                DataCell(
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Icons.visibility),
                                        tooltip: 'users.view'.tr(),
                                        onPressed: () => _viewUserDetails(user),
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.message),
                                        tooltip: 'users.send_message'.tr(),
                                        onPressed: () => _sendMessage(user),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadOnlineUsers,
        tooltip: 'common.refresh'.tr(),
        child: const Icon(Icons.refresh),
      ),
    );
  }

  // Show a dialog to compose and send a direct message to the selected user
  void _sendMessage(User user) {
    final TextEditingController messageController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('users.send_message_to'.tr(args: [user.username])),
          content: TextField(
            controller: messageController,
            maxLines: 5,
            decoration: InputDecoration(hintText: 'users.enter_message'.tr()),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('common.cancel'.tr()),
            ),
            ElevatedButton(
              onPressed: () async {
                final message = messageController.text.trim();
                if (message.isEmpty) {
                  ToastService.showWarning('users.message_empty'.tr());
                  return;
                }

                Navigator.pop(context);
                try {
                  final success = await _usersService.sendMessage(
                    user.id,
                    message,
                  );
                  if (success) {
                    ToastService.showSuccess('users.message_sent'.tr());
                  } else {
                    ToastService.showError('users.message_send_error'.tr());
                  }
                } catch (e) {
                  ToastService.showError(
                    'users.message_send_error'.tr(args: [e.toString()]),
                  );
                }
              },
              child: Text('users.send'.tr()),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    // Clean up any resources
    super.dispose();
  }
}
