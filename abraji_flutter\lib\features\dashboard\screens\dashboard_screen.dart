import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:go_router/go_router.dart';

import '../../../core/auth/auth_service.dart';
import '../../../core/di/injection_container.dart' as di;
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/widgets/logo.dart';
import '../../../shared/widgets/toast.dart';
import '../../../shared/theme/app_theme.dart';

class DashboardScreen extends StatefulWidget {
  final VoidCallback toggleTheme;

  const DashboardScreen({super.key, required this.toggleTheme});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  Widget build(BuildContext context) {
    return _buildDashboard();
  }

  Widget _buildDashboard() {
    final theme = Theme.of(context);
    final authService = di.sl<AuthService>();
    final user = authService.currentUser;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome section
          Text(
            '${'dashboard.welcome'.tr()}, ${user?.username ?? 'User'}',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 32),

          // Main Statistics Grid (matching abraji_web layout)
          _buildMainStatsGrid(theme),
          const SizedBox(height: 32),

          // Cards Statistics Grid
          _buildCardsStatsGrid(theme),
          const SizedBox(height: 32),

          // Transaction Statistics Grid
          _buildTransactionStatsGrid(theme),
        ],
      ),
    );
  }

  // Main statistics grid matching abraji_web
  Widget _buildMainStatsGrid(ThemeData theme) {
    return GridView.count(
      crossAxisCount: _getGridCrossAxisCount(),
      crossAxisSpacing: 24,
      mainAxisSpacing: 24,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.2,
      children: [
        _buildStatCard(
          title: 'dashboard.totalUsers'.tr(),
          value: '1,234',
          icon: Icons.people,
          gradientColors: [AppTheme.orange600, AppTheme.orange400],
          shadowColor: AppTheme.orange500.withValues(alpha: 0.4),
          onTap: () => context.go('/users'),
        ),
        _buildStatCard(
          title: 'dashboard.activeUsers'.tr(),
          value: '987',
          icon: Icons.person_pin,
          gradientColors: [AppTheme.emerald600, AppTheme.emerald400],
          shadowColor: AppTheme.emerald500.withValues(alpha: 0.4),
          onTap: () => context.go('/users'),
        ),
        _buildStatCard(
          title: 'dashboard.onlineUsers'.tr(),
          value: '567',
          icon: Icons.wifi,
          gradientColors: [AppTheme.rose600, AppTheme.rose400],
          shadowColor: AppTheme.rose500.withValues(alpha: 0.4),
          onTap: () => context.go('/users/online'),
        ),
        _buildStatCard(
          title: 'dashboard.onlineFUP'.tr(),
          value: '123',
          icon: Icons.lightbulb,
          gradientColors: [AppTheme.sky600, AppTheme.sky400],
          shadowColor: AppTheme.sky500.withValues(alpha: 0.4),
        ),
      ],
    );
  }

  // Cards statistics grid matching abraji_web
  Widget _buildCardsStatsGrid(ThemeData theme) {
    return GridView.count(
      crossAxisCount: _getGridCrossAxisCount(),
      crossAxisSpacing: 24,
      mainAxisSpacing: 24,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.2,
      children: [
        _buildCardStatCard(
          title: 'Card Profile 1',
          total: 1000,
          used: 750,
          remaining: 250,
        ),
        _buildCardStatCard(
          title: 'Card Profile 2',
          total: 500,
          used: 300,
          remaining: 200,
        ),
        _buildCardStatCard(
          title: 'Card Profile 3',
          total: 2000,
          used: 1200,
          remaining: 800,
        ),
        _buildCardStatCard(
          title: 'Card Profile 4',
          total: 800,
          used: 600,
          remaining: 200,
        ),
      ],
    );
  }

  // Transaction statistics grid matching abraji_web
  Widget _buildTransactionStatsGrid(ThemeData theme) {
    return GridView.count(
      crossAxisCount: _getGridCrossAxisCount(),
      crossAxisSpacing: 24,
      mainAxisSpacing: 24,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.2,
      children: [
        _buildStatCard(
          title: 'dashboard.maintenanceExpenses'.tr(),
          value: '50,000 IQD',
          icon: Icons.build,
          gradientColors: [AppTheme.purple600, AppTheme.purple400],
          shadowColor: AppTheme.purple500.withValues(alpha: 0.4),
        ),
        _buildStatCard(
          title: 'dashboard.expenseTransactions'.tr(),
          value: '25,000 IQD',
          icon: Icons.receipt,
          gradientColors: [AppTheme.indigo600, AppTheme.indigo400],
          shadowColor: AppTheme.indigo500.withValues(alpha: 0.4),
        ),
        _buildStatCard(
          title: 'dashboard.generalExpenses'.tr(),
          value: '75,000 IQD',
          icon: Icons.account_balance_wallet,
          gradientColors: [AppTheme.teal600, AppTheme.teal400],
          shadowColor: AppTheme.teal500.withValues(alpha: 0.4),
        ),
      ],
    );
  }

  // Helper method to get grid cross axis count based on screen size
  int _getGridCrossAxisCount() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 1200) return 4; // xl
    if (screenWidth >= 768) return 2; // md
    return 1; // mobile
  }

  // Build individual stat card matching abraji_web design
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required List<Color> gradientColors,
    required Color shadowColor,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Gradient icon container
            Positioned(
              top: -16,
              left: 16,
              child: Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradientColors,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: shadowColor,
                      blurRadius: 16,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Icon(icon, color: Colors.white, size: 28),
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const SizedBox(height: 16),
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.right,
                  ),
                  const Spacer(),
                  Container(
                    width: double.infinity,
                    height: 1,
                    color: theme.colorScheme.outline.withOpacity(0.2),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    value,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build card stat card for card statistics
  Widget _buildCardStatCard({
    required String title,
    required int total,
    required int used,
    required int remaining,
  }) {
    final theme = Theme.of(context);
    final usagePercentage = total > 0 ? (used / total) : 0.0;

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Gradient icon container
          Positioned(
            top: -16,
            left: 16,
            child: Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppTheme.blue600, AppTheme.blue400],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.blue500.withValues(alpha: 0.4),
                    blurRadius: 16,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: const Icon(
                Icons.credit_card,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const SizedBox(height: 16),
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.right,
                ),
                const SizedBox(height: 8),
                // Progress bar
                LinearProgressIndicator(
                  value: usagePercentage,
                  backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    usagePercentage > 0.8 ? Colors.red : AppTheme.emerald500,
                  ),
                ),
                const SizedBox(height: 8),
                // Usage stats
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          used.toString(),
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.emerald600,
                          ),
                        ),
                        Text(
                          'dashboard.used'.tr(),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.emerald600,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          remaining.toString(),
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        Text(
                          'dashboard.remaining'.tr(),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
