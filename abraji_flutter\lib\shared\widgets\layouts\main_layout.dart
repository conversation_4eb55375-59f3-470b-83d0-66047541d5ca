import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

import '../header/app_header.dart';
import '../sidebar/app_sidebar.dart';
import '../footer/app_footer.dart';

class MainLayout extends StatefulWidget {
  final Widget child;
  final VoidCallback toggleTheme;

  const MainLayout({
    super.key,
    required this.child,
    required this.toggleTheme,
  });

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isArabic = context.locale.languageCode == 'ar';
    
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: theme.colorScheme.surface,
      body: Row(
        children: [
          // Sidebar - Fixed on desktop, drawer on mobile
          if (MediaQuery.of(context).size.width >= 768)
            Container(
              width: 256, // w-64 equivalent (64 * 4 = 256px)
              height: double.infinity,
              child: AppSidebar(toggleTheme: widget.toggleTheme),
            ),
          
          // Main content area
          Expanded(
            child: Column(
              children: [
                // Header
                AppHeader(
                  scaffoldKey: _scaffoldKey,
                  toggleTheme: widget.toggleTheme,
                ),
                
                // Main content with padding
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16), // p-4 equivalent
                    margin: EdgeInsets.only(
                      top: 16, // mt-16 equivalent (header height compensation)
                    ),
                    child: Column(
                      children: [
                        // Page content
                        Expanded(child: widget.child),
                        
                        // Footer
                        const AppFooter(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      
      // Mobile drawer
      drawer: MediaQuery.of(context).size.width < 768
          ? Drawer(
              child: AppSidebar(toggleTheme: widget.toggleTheme),
            )
          : null,
    );
  }
}
