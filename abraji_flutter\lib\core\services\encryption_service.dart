import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter/foundation.dart';

class EncryptionService {
  static const String _encryptionKey = 'abcdefghijuklmno0123456789012345';
  static const String _encryptionQRKey = 'abc';

  late final encrypt.Encrypter _encrypter;
  late final encrypt.IV _iv;

  EncryptionService() {
    final key = encrypt.Key.fromBase64(base64.encode(_encryptionKey.codeUnits));
    _encrypter = encrypt.Encrypter(encrypt.AES(key));
    _iv = encrypt.IV.fromSecureRandom(16);
  }

  /// Generate encrypted payload compatible with CryptoJS format (static method)
  static String generatePayload(Map<String, dynamic> data) {
    try {
      // Use CryptoJS compatible encryption
      const passphrase = _encryptionKey;
      final jsonString = json.encode(data);

      // Generate random salt (8 bytes)
      final salt = encrypt.SecureRandom(8).bytes;

      // Derive key and IV from passphrase and salt (similar to CryptoJS)
      final keyIv = _deriveKeyAndIV(passphrase, salt);
      final key = encrypt.Key(Uint8List.fromList(keyIv.sublist(0, 32)));
      final iv = encrypt.IV(Uint8List.fromList(keyIv.sublist(32, 48)));

      final encrypter = encrypt.Encrypter(
        encrypt.AES(key, mode: encrypt.AESMode.cbc),
      );

      final encrypted = encrypter.encrypt(jsonString, iv: iv);

      // Format like CryptoJS: "Salted__" + salt + encrypted_data
      final salted = utf8.encode('Salted__');
      final combined = salted + salt + encrypted.bytes;

      return base64.encode(combined);
    } catch (e) {
      // Log error in development mode
      if (kDebugMode) {
        debugPrint('Error generating payload: $e');
      }
      return base64.encode(utf8.encode(json.encode(data)));
    }
  }

  /// Derive key and IV from passphrase and salt (CryptoJS compatible)
  static List<int> _deriveKeyAndIV(String passphrase, List<int> salt) {
    final passphraseBytes = utf8.encode(passphrase);
    final combined = passphraseBytes + salt;

    // MD5 hash iterations (similar to CryptoJS)
    var hash = _md5Hash(combined);
    final result = List<int>.from(hash);

    while (result.length < 48) {
      // 32 bytes key + 16 bytes IV
      hash = _md5Hash(hash + combined);
      result.addAll(hash);
    }

    return result.sublist(0, 48);
  }

  /// MD5 hash implementation using crypto package
  static List<int> _md5Hash(List<int> data) {
    final digest = md5.convert(data);
    return digest.bytes;
  }

  /// Decrypt payload
  Map<String, dynamic>? decryptPayload(String encryptedData, String ivString) {
    try {
      final iv = encrypt.IV.fromBase64(ivString);
      final encrypted = encrypt.Encrypted.fromBase64(encryptedData);
      final decrypted = _encrypter.decrypt(encrypted, iv: iv);
      return json.decode(decrypted);
    } catch (e) {
      return null;
    }
  }

  /// Generate QR encryption (simplified version)
  String encryptQR(String data) {
    try {
      final bytes = utf8.encode(data + _encryptionQRKey);
      final digest = sha256.convert(bytes);
      return digest.toString();
    } catch (e) {
      return data;
    }
  }

  /// Generate random string for additional security
  String generateRandomString(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }
}
