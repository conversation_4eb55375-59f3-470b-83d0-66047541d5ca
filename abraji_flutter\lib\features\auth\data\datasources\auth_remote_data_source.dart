import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/services/api_service.dart';
import '../../../../shared/constants/app_constants.dart';
import '../models/auth_response_model.dart';
import '../models/user_model.dart';

/// Authentication Remote Data Source Interface
abstract class AuthRemoteDataSource {
  Future<AuthResponseModel> login({
    required String username,
    required String password,
    String? domain,
    bool rememberMe = false,
  });

  Future<void> logout();

  Future<UserModel> getCurrentUser();

  Future<AuthResponseModel> refreshToken();
}

/// Authentication Remote Data Source Implementation
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final ApiService apiService;

  AuthRemoteDataSourceImpl({required this.apiService});

  @override
  Future<AuthResponseModel> login({
    required String username,
    required String password,
    String? domain,
    bool rememberMe = false,
  }) async {
    try {
      // Prepare login data
      final loginData = {
        'username': username,
        'password': password,
        'rememberMe': rememberMe,
      };

      // Determine target domain with fallback logic
      String targetDomain;
      if (domain != null && domain.isNotEmpty && domain != '***********') {
        targetDomain = domain;
      } else {
        // Use localhost:8080 for mock API testing
        targetDomain = 'http://localhost:8080/api';
      }

      // Use ApiService to send encrypted payload
      final response = await apiService.postWithPayload(
        AppConstants.loginEndpoint,
        loginData,
        apiUrl: targetDomain,
      );

      if (response.statusCode == 200) {
        return AuthResponseModel.fromJson(response.data);
      } else {
        throw ServerException(
          message: 'Login failed with status code: ${response.statusCode}',
          code: 'LOGIN_FAILED',
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error during login: $e',
        code: 'UNEXPECTED_ERROR',
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      await apiService.post(AppConstants.logoutEndpoint, data: {});
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error during logout: $e',
        code: 'UNEXPECTED_ERROR',
      );
    }
  }

  @override
  Future<UserModel> getCurrentUser() async {
    try {
      final response = await apiService.get('/auth/user');

      if (response.statusCode == 200) {
        return UserModel.fromJson(response.data);
      } else {
        throw ServerException(
          message:
              'Failed to get current user with status code: ${response.statusCode}',
          code: 'GET_USER_FAILED',
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error getting current user: $e',
        code: 'UNEXPECTED_ERROR',
      );
    }
  }

  @override
  Future<AuthResponseModel> refreshToken() async {
    try {
      final response = await apiService.post('/auth/refresh', data: {});

      if (response.statusCode == 200) {
        return AuthResponseModel.fromJson(response.data);
      } else {
        throw ServerException(
          message:
              'Token refresh failed with status code: ${response.statusCode}',
          code: 'REFRESH_FAILED',
        );
      }
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error during token refresh: $e',
        code: 'UNEXPECTED_ERROR',
      );
    }
  }

  /// Handle Dio exceptions and convert them to appropriate app exceptions
  ServerException _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const ServerException(
          message: 'Connection timeout. Please check your internet connection.',
          code: 'TIMEOUT_ERROR',
        );
      case DioExceptionType.connectionError:
        return const ServerException(
          message: 'No internet connection. Please check your network.',
          code: 'CONNECTION_ERROR',
        );
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Server error occurred';

        switch (statusCode) {
          case 400:
            return ServerException(message: message, code: 'BAD_REQUEST');
          case 401:
            return const ServerException(
              message:
                  'Invalid credentials. Please check your username and password.',
              code: 'UNAUTHORIZED',
            );
          case 403:
            return const ServerException(
              message:
                  'Access forbidden. You do not have permission to perform this action.',
              code: 'FORBIDDEN',
            );
          case 404:
            return const ServerException(
              message: 'The requested resource was not found.',
              code: 'NOT_FOUND',
            );
          case 422:
            return ServerException(message: message, code: 'VALIDATION_ERROR');
          case 429:
            return const ServerException(
              message: 'Too many requests. Please try again later.',
              code: 'RATE_LIMIT_EXCEEDED',
            );
          case 500:
          case 502:
          case 503:
          case 504:
            return const ServerException(
              message: 'Server error. Please try again later.',
              code: 'SERVER_ERROR',
            );
          default:
            return ServerException(
              message: message,
              code: 'HTTP_ERROR_$statusCode',
            );
        }
      case DioExceptionType.cancel:
        return const ServerException(
          message: 'Request was cancelled.',
          code: 'REQUEST_CANCELLED',
        );
      case DioExceptionType.unknown:
      default:
        return ServerException(
          message: 'An unexpected error occurred: ${e.message}',
          code: 'UNKNOWN_ERROR',
        );
    }
  }
}
