import 'package:flutter/material.dart';

class AppTheme {
  // Primary colors matching abraji_web tailwind.config.js
  static const Color primaryColor = Color(0xFF1D4ED8); // blue-700 (primary-700)
  static const Color primaryLightColor = Color(
    0xFF2563EB,
  ); // blue-600 (primary-600)
  static const Color primaryDarkColor = Color(
    0xFF1E40AF,
  ); // blue-800 (primary-800)
  static const Color primary50 = Color(0xFFEFF6FF); // primary-50
  static const Color primary100 = Color(0xFFDBEAFE); // primary-100
  static const Color primary200 = Color(0xFFBFDBFE); // primary-200
  static const Color primary300 = Color(0xFF93C5FD); // primary-300
  static const Color primary400 = Color(0xFF60A5FA); // primary-400
  static const Color primary500 = Color(0xFF3B82F6); // primary-500
  static const Color primary950 = Color(0xFF172554); // primary-950

  // Secondary color (emerald from tailwind.config.js)
  static const Color secondaryColor = Color(0xFF059669); // emerald-600
  static const Color secondaryLightColor = Color(0xFF10B981); // emerald-500
  static const Color secondaryDarkColor = Color(0xFF047857); // emerald-700
  static const Color emerald50 = Color(0xFFECFDF5); // emerald-50
  static const Color emerald100 = Color(0xFFD1FAE5); // emerald-100
  static const Color emerald200 = Color(0xFFA7F3D0); // emerald-200
  static const Color emerald300 = Color(0xFF6EE7B7); // emerald-300
  static const Color emerald400 = Color(0xFF34D399); // emerald-400
  static const Color emerald500 = Color(0xFF10B981); // emerald-500
  static const Color emerald600 = Color(0xFF059669); // emerald-600
  static const Color emerald800 = Color(0xFF065F46); // emerald-800
  static const Color emerald900 = Color(0xFF064E3B); // emerald-900

  // Yellow colors from tailwind.config.js
  static const Color yellow50 = Color(0xFFFEFCE8); // yellow-50
  static const Color yellow100 = Color(0xFFFEF9C3); // yellow-100
  static const Color yellow200 = Color(0xFFFEF08A); // yellow-200
  static const Color yellow300 = Color(0xFFFDE047); // yellow-300
  static const Color yellow400 = Color(0xFFFACC15); // yellow-400
  static const Color yellow500 = Color(0xFFEAB308); // yellow-500
  static const Color yellow600 = Color(0xFFCA8A04); // yellow-600
  static const Color yellow700 = Color(0xFFA16207); // yellow-700
  static const Color yellow800 = Color(0xFF7F552F); // yellow-800

  // Neutral colors matching abraji_web
  static const Color backgroundColor = Color(0xFFF9FAFB); // gray-50
  static const Color surfaceColor = Colors.white;
  static const Color errorColor = Color(0xFFDC2626); // red-600
  static const Color textPrimaryColor = Color(0xFF1F2937); // gray-800
  static const Color textSecondaryColor = Color(0xFF6B7280); // gray-500
  static const Color gray100 = Color(0xFFF3F4F6); // gray-100
  static const Color gray200 = Color(0xFFE5E7EB); // gray-200
  static const Color gray300 = Color(0xFFD1D5DB); // gray-300
  static const Color gray400 = Color(0xFF9CA3AF); // gray-400
  static const Color gray600 = Color(0xFF4B5563); // gray-600
  static const Color gray700 = Color(0xFF374151); // gray-700
  static const Color gray900 = Color(0xFF111827); // gray-900

  // Custom gradient colors from tailwind.config.js
  static const Color mainColor = Color(0xFF4A00E0); // main-color
  static const Color secondaryColorGradient = Color(
    0xFF8E2DE2,
  ); // secondary-color

  // Additional colors for dashboard cards
  // Orange colors
  static const Color orange400 = Color(0xFFFB923C); // orange-400
  static const Color orange500 = Color(0xFFF97316); // orange-500
  static const Color orange600 = Color(0xFFEA580C); // orange-600

  // Rose colors
  static const Color rose400 = Color(0xFFFB7185); // rose-400
  static const Color rose500 = Color(0xFFF43F5E); // rose-500
  static const Color rose600 = Color(0xFFE11D48); // rose-600

  // Sky colors
  static const Color sky400 = Color(0xFF38BDF8); // sky-400
  static const Color sky500 = Color(0xFF0EA5E9); // sky-500
  static const Color sky600 = Color(0xFF0284C7); // sky-600

  // Purple colors
  static const Color purple400 = Color(0xFFA855F7); // purple-400
  static const Color purple500 = Color(0xFF9333EA); // purple-500
  static const Color purple600 = Color(0xFF7C3AED); // purple-600

  // Indigo colors
  static const Color indigo400 = Color(0xFF818CF8); // indigo-400
  static const Color indigo500 = Color(0xFF6366F1); // indigo-500
  static const Color indigo600 = Color(0xFF4F46E5); // indigo-600

  // Teal colors
  static const Color teal400 = Color(0xFF2DD4BF); // teal-400
  static const Color teal500 = Color(0xFF14B8A6); // teal-500
  static const Color teal600 = Color(0xFF0D9488); // teal-600

  // Blue colors
  static const Color blue400 = Color(0xFF60A5FA); // blue-400
  static const Color blue500 = Color(0xFF3B82F6); // blue-500
  static const Color blue600 = Color(0xFF2563EB); // blue-600

  // Light theme matching abraji_web
  static ThemeData lightTheme = ThemeData(
    primaryColor: primaryColor,
    colorScheme: const ColorScheme.light(
      primary: primaryColor,
      primaryContainer: primary100,
      secondary: secondaryColor,
      secondaryContainer: emerald100,
      surface: surfaceColor,
      surfaceContainerHighest: gray100,
      error: errorColor,
      onPrimary: Colors.white,
      onPrimaryContainer: primaryDarkColor,
      onSecondary: Colors.white,
      onSecondaryContainer: secondaryDarkColor,
      onSurface: textPrimaryColor,
      onSurfaceVariant: textSecondaryColor,
      onError: Colors.white,
      outline: gray300,
      outlineVariant: gray200,
      brightness: Brightness.light,
    ),
    scaffoldBackgroundColor: backgroundColor,
    appBarTheme: const AppBarTheme(
      backgroundColor: surfaceColor,
      foregroundColor: textPrimaryColor,
      elevation: 0,
      shadowColor: Colors.black12,
      surfaceTintColor: Colors.transparent,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: const BorderSide(color: primaryColor),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Colors.white,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      focusedBorder: const OutlineInputBorder(
        borderRadius: BorderRadius.all(Radius.circular(8)),
        borderSide: BorderSide(color: primaryColor),
      ),
      errorBorder: const OutlineInputBorder(
        borderRadius: BorderRadius.all(Radius.circular(8)),
        borderSide: BorderSide(color: errorColor),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    ),
    // cardTheme intentionally omitted due to API mismatch
    dividerTheme: DividerThemeData(color: Colors.grey.shade200, thickness: 1),
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        color: textPrimaryColor,
        fontWeight: FontWeight.bold,
      ),
      displayMedium: TextStyle(
        color: textPrimaryColor,
        fontWeight: FontWeight.bold,
      ),
      displaySmall: TextStyle(
        color: textPrimaryColor,
        fontWeight: FontWeight.bold,
      ),
      headlineLarge: TextStyle(
        color: textPrimaryColor,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: TextStyle(
        color: textPrimaryColor,
        fontWeight: FontWeight.bold,
      ),
      headlineSmall: TextStyle(
        color: textPrimaryColor,
        fontWeight: FontWeight.w600,
      ),
      titleLarge: TextStyle(
        color: textPrimaryColor,
        fontWeight: FontWeight.w600,
      ),
      titleMedium: TextStyle(
        color: textPrimaryColor,
        fontWeight: FontWeight.w600,
      ),
      titleSmall: TextStyle(
        color: textPrimaryColor,
        fontWeight: FontWeight.w500,
      ),
      bodyLarge: TextStyle(color: textPrimaryColor),
      bodyMedium: TextStyle(color: textPrimaryColor),
      bodySmall: TextStyle(color: textSecondaryColor),
      labelLarge: TextStyle(
        color: textPrimaryColor,
        fontWeight: FontWeight.w500,
      ),
      labelMedium: TextStyle(color: textPrimaryColor),
      labelSmall: TextStyle(color: textSecondaryColor),
    ),
  );

  // Dark theme matching abraji_web
  static ThemeData darkTheme = ThemeData(
    primaryColor: primaryColor,
    colorScheme: const ColorScheme.dark(
      primary: primaryColor,
      primaryContainer: primary950,
      secondary: secondaryColor,
      secondaryContainer: emerald900,
      surface: gray700, // gray-700
      surfaceContainerHighest: gray600,
      error: errorColor,
      onPrimary: Colors.white,
      onPrimaryContainer: primary200,
      onSecondary: Colors.white,
      onSecondaryContainer: emerald200,
      onSurface: Colors.white,
      onSurfaceVariant: gray300,
      onError: Colors.white,
      outline: gray600,
      outlineVariant: gray700,
      brightness: Brightness.dark,
    ),
    scaffoldBackgroundColor: gray900, // gray-900
    appBarTheme: const AppBarTheme(
      backgroundColor: gray700, // gray-700
      foregroundColor: Colors.white,
      elevation: 0,
      shadowColor: Colors.black26,
      surfaceTintColor: Colors.transparent,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryLightColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryLightColor,
        side: const BorderSide(color: primaryLightColor),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: const Color(0xFF374151), // gray-700
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Color(0xFF4B5563)), // gray-600
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Color(0xFF4B5563)), // gray-600
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: primaryLightColor),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: errorColor),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      labelStyle: const TextStyle(color: Colors.white70),
      hintStyle: const TextStyle(color: Colors.white54),
    ),
    // cardTheme intentionally omitted in dark theme
    dividerTheme: const DividerThemeData(
      color: Color(0xFF374151), // gray-700
      thickness: 1,
    ),
    textTheme: const TextTheme(
      displayLarge: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
      displayMedium: TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
      ),
      displaySmall: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
      headlineLarge: TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
      ),
      headlineSmall: TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.w600,
      ),
      titleLarge: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
      titleMedium: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
      titleSmall: TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
      bodyLarge: TextStyle(color: Colors.white),
      bodyMedium: TextStyle(color: Colors.white),
      bodySmall: TextStyle(color: Colors.white70),
      labelLarge: TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
      labelMedium: TextStyle(color: Colors.white),
      labelSmall: TextStyle(color: Colors.white70),
    ),
  );
}
