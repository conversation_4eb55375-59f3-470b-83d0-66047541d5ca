
[2025-07-07 10:29:02] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 10:29:02] local.INFO: Request data: {"payload":"U2FsdGVkX1+NrogeV1ZxDFj6n6AS7dFomUp+NXEWzVNNnV7EtC\/b\/JWmiQFzeWkLlZ3\/\/rtS\/ml9ONokizTpJwLhp6WGD9JWe6enuXZB4JOIy6hxLrWBmt87E1KCRqK3","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 10:29:02] local.INFO: Validation passed  
[2025-07-07 10:29:02] local.INFO: API Domain: http://***********  
[2025-07-07 10:29:02] local.INFO: API Domain: http://***********  
[2025-07-07 10:29:02] local.INFO: Is localhost: false  
[2025-07-07 10:29:02] local.INFO: Port: 80  
[2025-07-07 10:29:02] local.INFO: Is same port: false  
[2025-07-07 10:29:02] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 10:29:02] local.INFO: Request payload: U2FsdGVkX1+NrogeV1ZxDFj6n6AS7dFomUp+NXEWzVNNnV7EtC/b/JWmiQFzeWkLlZ3//rtS/ml9ONokizTpJwLhp6WGD9JWe6enuXZB4JOIy6hxLrWBmt87E1KCRqK3  
[2025-07-07 10:30:16] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 10:30:16] local.INFO: Request data: {"payload":"U2FsdGVkX1976afKRDft9xYDbtliMRzEWl3eqogH+fSomBeXbm8YeCeAwSXi1MxlCLkK27drKQLsW1qjOV\/oR8X2CwsRCkqqleO8xEqImZ4=","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 10:30:16] local.INFO: Validation passed  
[2025-07-07 10:30:16] local.INFO: API Domain: http://***********  
[2025-07-07 10:30:16] local.INFO: API Domain: http://***********  
[2025-07-07 10:30:16] local.INFO: Is localhost: false  
[2025-07-07 10:30:16] local.INFO: Port: 80  
[2025-07-07 10:30:16] local.INFO: Is same port: false  
[2025-07-07 10:30:16] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 10:30:16] local.INFO: Request payload: U2FsdGVkX1976afKRDft9xYDbtliMRzEWl3eqogH+fSomBeXbm8YeCeAwSXi1MxlCLkK27drKQLsW1qjOV/oR8X2CwsRCkqqleO8xEqImZ4=  
[2025-07-07 10:30:26] local.INFO: UsersService: Attempting to connect to external API {"url":"http://127.0.0.1:8000/admin/api/index.php/api/index/user","api_domain":"http://127.0.0.1:8000","has_authorization":false,"payload_length":64} 
[2025-07-07 10:30:56] local.ERROR: UsersService: Connection failed to external API {"url":"http://127.0.0.1:8000/admin/api/index.php/api/index/user","api_domain":"http://127.0.0.1:8000","error":"cURL error 28: Operation timed out after 30004 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:8000/admin/api/index.php/api/index/user"} 
[2025-07-07 10:30:59] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://127.0.0.1:8000/admin/api/index.php/api/index/online","api_domain":"http://127.0.0.1:8000","has_authorization":false} 
[2025-07-07 10:31:29] local.ERROR: UsersService: Connection failed for online users {"url":"http://127.0.0.1:8000/admin/api/index.php/api/index/online","api_domain":"http://127.0.0.1:8000","error":"cURL error 28: Operation timed out after 30069 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:8000/admin/api/index.php/api/index/online"} 
[2025-07-07 10:31:47] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://127.0.0.1:8000/admin/api/index.php/api/index/online","api_domain":"http://127.0.0.1:8000","has_authorization":false} 
[2025-07-07 10:32:17] local.ERROR: UsersService: Connection failed for online users {"url":"http://127.0.0.1:8000/admin/api/index.php/api/index/online","api_domain":"http://127.0.0.1:8000","error":"cURL error 28: Operation timed out after 30053 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:8000/admin/api/index.php/api/index/online"} 
[2025-07-07 10:32:19] local.INFO: UsersService: Attempting to connect to external API {"url":"http://127.0.0.1:8000/admin/api/index.php/api/index/user","api_domain":"http://127.0.0.1:8000","has_authorization":false,"payload_length":64} 
[2025-07-07 10:32:50] local.ERROR: UsersService: Connection failed to external API {"url":"http://127.0.0.1:8000/admin/api/index.php/api/index/user","api_domain":"http://127.0.0.1:8000","error":"cURL error 28: Operation timed out after 30009 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:8000/admin/api/index.php/api/index/user"} 
[2025-07-07 10:33:36] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 10:33:36] local.INFO: Request data: {"payload":"U2FsdGVkX18icWpCY+SmwA1oZiTQgRy2vtBxn9ho1NH49lma1\/jjq8zVTqwLj8xmW62RuwEI0QzY\/UdtvVUoOfARZq+Jlg96Ov8f7Tf4bT0=","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 10:33:36] local.INFO: Validation passed  
[2025-07-07 10:33:36] local.INFO: API Domain: http://***********  
[2025-07-07 10:33:36] local.INFO: API Domain: http://***********  
[2025-07-07 10:33:36] local.INFO: Is localhost: false  
[2025-07-07 10:33:36] local.INFO: Port: 80  
[2025-07-07 10:33:36] local.INFO: Is same port: false  
[2025-07-07 10:33:36] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 10:33:36] local.INFO: Request payload: U2FsdGVkX18icWpCY+SmwA1oZiTQgRy2vtBxn9ho1NH49lma1/jjq8zVTqwLj8xmW62RuwEI0QzY/UdtvVUoOfARZq+Jlg96Ov8f7Tf4bT0=  
[2025-07-07 10:34:06] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 10:34:06] local.INFO: Request data: {"payload":"U2FsdGVkX19SR7PyRJx1\/Ew0i4UBrtLVOno7o+HsxXyIZqPtSbpCNpS19JZLqC2rN0nSBHsTvRz6Fz\/E+Rf\/iMPo3\/Td25Zbte3OoxhivPk=","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 10:34:06] local.INFO: Validation passed  
[2025-07-07 10:34:06] local.INFO: API Domain: http://***********  
[2025-07-07 10:34:06] local.INFO: API Domain: http://***********  
[2025-07-07 10:34:06] local.INFO: Is localhost: false  
[2025-07-07 10:34:06] local.INFO: Port: 80  
[2025-07-07 10:34:06] local.INFO: Is same port: false  
[2025-07-07 10:34:06] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 10:34:06] local.INFO: Request payload: U2FsdGVkX19SR7PyRJx1/Ew0i4UBrtLVOno7o+HsxXyIZqPtSbpCNpS19JZLqC2rN0nSBHsTvRz6Fz/E+Rf/iMPo3/Td25Zbte3OoxhivPk=  
[2025-07-07 10:34:33] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 10:34:33] local.INFO: Request data: {"payload":"U2FsdGVkX18N7+sv\/9AI6B7fbPSOURS0R8OzJxAiXmh1GQHtWsrW0YXlNKWwMs8Jq14gDLupSqPcmRLta6Hkusefl9d\/MFrIC0AxVEWTi3A=","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 10:34:33] local.INFO: Validation passed  
[2025-07-07 10:34:33] local.INFO: API Domain: http://***********  
[2025-07-07 10:34:33] local.INFO: API Domain: http://***********  
[2025-07-07 10:34:33] local.INFO: Is localhost: false  
[2025-07-07 10:34:33] local.INFO: Port: 80  
[2025-07-07 10:34:33] local.INFO: Is same port: false  
[2025-07-07 10:34:33] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 10:34:33] local.INFO: Request payload: U2FsdGVkX18N7+sv/9AI6B7fbPSOURS0R8OzJxAiXmh1GQHtWsrW0YXlNKWwMs8Jq14gDLupSqPcmRLta6Hkusefl9d/MFrIC0AxVEWTi3A=  
[2025-07-07 10:34:42] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 10:34:42] local.INFO: Request data: {"payload":"U2FsdGVkX185XkrvrpuJFOBupwhAe50NsGWs5r\/jdKCAkosoSenElW1WLGzEsADC3atKeo90GqKAMDMUK8W38FfCvQV1HKx\/reDytTVFUMo=","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 10:34:42] local.INFO: Validation passed  
[2025-07-07 10:34:42] local.INFO: API Domain: http://***********  
[2025-07-07 10:34:42] local.INFO: API Domain: http://***********  
[2025-07-07 10:34:42] local.INFO: Is localhost: false  
[2025-07-07 10:34:42] local.INFO: Port: 80  
[2025-07-07 10:34:42] local.INFO: Is same port: false  
[2025-07-07 10:34:42] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 10:34:42] local.INFO: Request payload: U2FsdGVkX185XkrvrpuJFOBupwhAe50NsGWs5r/jdKCAkosoSenElW1WLGzEsADC3atKeo90GqKAMDMUK8W38FfCvQV1HKx/reDytTVFUMo=  
[2025-07-07 10:34:50] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:35:20] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30003 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:35:48] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:36:18] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30007 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:36:48] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:37:18] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30006 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:37:49] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:38:19] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30013 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:38:49] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:39:19] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30014 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:39:49] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:40:19] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30002 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:40:49] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:41:19] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30014 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:41:49] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:42:19] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30011 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:42:49] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:43:19] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30002 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:43:49] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:44:19] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30022 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:44:34] local.INFO: UsersService: Attempting to connect to external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/user","api_domain":"http://localhost:8080/api","has_authorization":false,"payload_length":64} 
[2025-07-07 10:45:04] local.ERROR: UsersService: Connection failed to external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/user","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30017 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/user"} 
[2025-07-07 10:49:00] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 10:49:01] local.INFO: Request data: {"payload":"U2FsdGVkX18M0pvbOJBgZb6NXeci4Kv8rCszabn9xcp3fVh4W8FRMoXsQcfOnHMEKub\/SDZb3lXRev1YecAiwh7YDI01B13hVjCzBvfEb68=","api_domain":"https:\/\/reseller.nbtel.iq","validated_api_domain":"https:\/\/reseller.nbtel.iq"}  
[2025-07-07 10:49:01] local.INFO: Validation passed  
[2025-07-07 10:49:01] local.INFO: API Domain: https://reseller.nbtel.iq  
[2025-07-07 10:49:01] local.INFO: API Domain: https://reseller.nbtel.iq  
[2025-07-07 10:49:01] local.INFO: Is localhost: false  
[2025-07-07 10:49:01] local.INFO: Port: 443  
[2025-07-07 10:49:01] local.INFO: Is same port: false  
[2025-07-07 10:49:01] local.INFO: Making request to external API: https://reseller.nbtel.iq/admin/api/index.php/api/login  
[2025-07-07 10:49:01] local.INFO: Request payload: U2FsdGVkX18M0pvbOJBgZb6NXeci4Kv8rCszabn9xcp3fVh4W8FRMoXsQcfOnHMEKub/SDZb3lXRev1YecAiwh7YDI01B13hVjCzBvfEb68=  
[2025-07-07 10:50:12] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 10:50:12] local.INFO: Request data: {"payload":"U2FsdGVkX1+WGuRCyLmdPfxWGrPik\/5LQCqa1lq+4y8Z9QnK78lkWEAOrVo+\/mlEnBMbw04FAsAULRsJMrSydYeMKTaC2rxeiqhIVVoHKZg=","api_domain":"https:\/\/reseller.nbtel.iq","validated_api_domain":"https:\/\/reseller.nbtel.iq"}  
[2025-07-07 10:50:12] local.INFO: Validation passed  
[2025-07-07 10:50:12] local.INFO: API Domain: https://reseller.nbtel.iq  
[2025-07-07 10:50:12] local.INFO: API Domain: https://reseller.nbtel.iq  
[2025-07-07 10:50:12] local.INFO: Is localhost: false  
[2025-07-07 10:50:12] local.INFO: Port: 443  
[2025-07-07 10:50:12] local.INFO: Is same port: false  
[2025-07-07 10:50:12] local.INFO: Making request to external API: https://reseller.nbtel.iq/admin/api/index.php/api/login  
[2025-07-07 10:50:12] local.INFO: Request payload: U2FsdGVkX1+WGuRCyLmdPfxWGrPik/5LQCqa1lq+4y8Z9QnK78lkWEAOrVo+/mlEnBMbw04FAsAULRsJMrSydYeMKTaC2rxeiqhIVVoHKZg=  
[2025-07-07 10:50:59] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 10:50:59] local.INFO: Request data: {"payload":"U2FsdGVkX19MEur6UZ2QF\/Td2K\/ibrN\/b5di7hnhJo78K3k7vMAk6b6cLFYfmWrbE2rKMMbHpKl0PMJ31izHempiletV3N+JdheFiRFLWiI=","api_domain":"https:\/\/reseller.nbtel.iq","validated_api_domain":"https:\/\/reseller.nbtel.iq"}  
[2025-07-07 10:50:59] local.INFO: Validation passed  
[2025-07-07 10:50:59] local.INFO: API Domain: https://reseller.nbtel.iq  
[2025-07-07 10:50:59] local.INFO: API Domain: https://reseller.nbtel.iq  
[2025-07-07 10:50:59] local.INFO: Is localhost: false  
[2025-07-07 10:50:59] local.INFO: Port: 443  
[2025-07-07 10:50:59] local.INFO: Is same port: false  
[2025-07-07 10:50:59] local.INFO: Making request to external API: https://reseller.nbtel.iq/admin/api/index.php/api/login  
[2025-07-07 10:50:59] local.INFO: Request payload: U2FsdGVkX19MEur6UZ2QF/Td2K/ibrN/b5di7hnhJo78K3k7vMAk6b6cLFYfmWrbE2rKMMbHpKl0PMJ31izHempiletV3N+JdheFiRFLWiI=  
[2025-07-07 10:51:27] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 10:51:27] local.INFO: Request data: {"payload":"U2FsdGVkX1\/+oawm4+qthnRTCm3DF8uvuSD2CIp7AavJrIGkKSy31BpjZGDD7rpm2i1sxKKbpmhPh1VyBSKxs\/p17Cys7Pd13RV2kiJHWUw=","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 10:51:27] local.INFO: Validation passed  
[2025-07-07 10:51:27] local.INFO: API Domain: http://***********  
[2025-07-07 10:51:27] local.INFO: API Domain: http://***********  
[2025-07-07 10:51:27] local.INFO: Is localhost: false  
[2025-07-07 10:51:27] local.INFO: Port: 80  
[2025-07-07 10:51:27] local.INFO: Is same port: false  
[2025-07-07 10:51:27] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 10:51:27] local.INFO: Request payload: U2FsdGVkX1/+oawm4+qthnRTCm3DF8uvuSD2CIp7AavJrIGkKSy31BpjZGDD7rpm2i1sxKKbpmhPh1VyBSKxs/p17Cys7Pd13RV2kiJHWUw=  
[2025-07-07 10:51:41] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:52:11] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30010 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:52:35] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:53:05] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30012 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:53:35] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:54:05] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30014 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:54:35] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:55:05] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30008 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:55:35] local.INFO: UsersService: Attempting to get online users from external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","has_authorization":false} 
[2025-07-07 10:56:05] local.ERROR: UsersService: Connection failed for online users {"url":"http://localhost:8080/api/admin/api/index.php/api/index/online","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30137 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/online"} 
[2025-07-07 10:58:31] local.INFO: UsersService: Attempting to connect to external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/user","api_domain":"http://localhost:8080/api","has_authorization":true,"payload_length":64} 
[2025-07-07 10:59:02] local.ERROR: UsersService: Connection failed to external API {"url":"http://localhost:8080/api/admin/api/index.php/api/index/user","api_domain":"http://localhost:8080/api","error":"cURL error 28: Operation timed out after 30003 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://localhost:8080/api/admin/api/index.php/api/index/user"} 
[2025-07-07 11:05:10] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 11:05:10] local.INFO: Request data: {"payload":"U2FsdGVkX1\/XOfpCGjg0oa6DqqAeM66u0BcEZ6mZ1uVUWxEAwJl\/2yyTk22KaUnrQlCTN7zrFTNVID8LOq\/SCI+CCHoo3bsR9eyz5IjokJ8=","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 11:05:10] local.INFO: Validation passed  
[2025-07-07 11:05:10] local.INFO: API Domain: http://***********  
[2025-07-07 11:05:10] local.INFO: API Domain: http://***********  
[2025-07-07 11:05:10] local.INFO: Is localhost: false  
[2025-07-07 11:05:10] local.INFO: Port: 80  
[2025-07-07 11:05:10] local.INFO: Is same port: false  
[2025-07-07 11:05:10] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 11:05:10] local.INFO: Request payload: U2FsdGVkX1/XOfpCGjg0oa6DqqAeM66u0BcEZ6mZ1uVUWxEAwJl/2yyTk22KaUnrQlCTN7zrFTNVID8LOq/SCI+CCHoo3bsR9eyz5IjokJ8=  
[2025-07-07 11:06:04] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 11:06:04] local.INFO: Request data: {"payload":"U2FsdGVkX18votWc8nT6lLCWNpNtSoOKOZfykwZkmVoSKHUby8Z9n4ILs5qJR+4GTTxqirp7hb1Hottvs486pbaSLbYuazZ+xmUnecp17p8=","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 11:06:05] local.INFO: Validation passed  
[2025-07-07 11:06:05] local.INFO: API Domain: http://***********  
[2025-07-07 11:06:05] local.INFO: API Domain: http://***********  
[2025-07-07 11:06:05] local.INFO: Is localhost: false  
[2025-07-07 11:06:05] local.INFO: Port: 80  
[2025-07-07 11:06:05] local.INFO: Is same port: false  
[2025-07-07 11:06:05] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 11:06:05] local.INFO: Request payload: U2FsdGVkX18votWc8nT6lLCWNpNtSoOKOZfykwZkmVoSKHUby8Z9n4ILs5qJR+4GTTxqirp7hb1Hottvs486pbaSLbYuazZ+xmUnecp17p8=  
[2025-07-07 11:06:36] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 11:06:36] local.INFO: Request data: {"payload":"U2FsdGVkX18sPUDiPJAURT9HbB0sdfIFHCgTniBnzhTGBVrQryc0r2vydMsU0lKC6CuLtOutr+r54rae5QU6kXmVS1U8uY+N3vfIARu1BQo=","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 11:06:36] local.INFO: Validation passed  
[2025-07-07 11:06:36] local.INFO: API Domain: http://***********  
[2025-07-07 11:06:36] local.INFO: API Domain: http://***********  
[2025-07-07 11:06:36] local.INFO: Is localhost: false  
[2025-07-07 11:06:36] local.INFO: Port: 80  
[2025-07-07 11:06:36] local.INFO: Is same port: false  
[2025-07-07 11:06:36] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 11:06:36] local.INFO: Request payload: U2FsdGVkX18sPUDiPJAURT9HbB0sdfIFHCgTniBnzhTGBVrQryc0r2vydMsU0lKC6CuLtOutr+r54rae5QU6kXmVS1U8uY+N3vfIARu1BQo=  
[2025-07-07 11:06:46] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 11:06:46] local.INFO: Request data: {"payload":"U2FsdGVkX1\/vDw0HiDOPddlgJuRzrX7SF8isv4DJvJbBnK2YJjuNQ6kpr3Ds43v4\/1V8H33Wb\/Ds+KMijARnC+k8oJxC+JOT5esB9Lnrm+k=","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 11:06:46] local.INFO: Validation passed  
[2025-07-07 11:06:46] local.INFO: API Domain: http://***********  
[2025-07-07 11:06:46] local.INFO: API Domain: http://***********  
[2025-07-07 11:06:46] local.INFO: Is localhost: false  
[2025-07-07 11:06:46] local.INFO: Port: 80  
[2025-07-07 11:06:46] local.INFO: Is same port: false  
[2025-07-07 11:06:46] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 11:06:46] local.INFO: Request payload: U2FsdGVkX1/vDw0HiDOPddlgJuRzrX7SF8isv4DJvJbBnK2YJjuNQ6kpr3Ds43v4/1V8H33Wb/Ds+KMijARnC+k8oJxC+JOT5esB9Lnrm+k=  
[2025-07-07 11:06:52] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 11:06:52] local.INFO: Request data: {"payload":"U2FsdGVkX1+bdfuMNidba8ohdk6L2qlYCyh1r4vZi2fKBhUbdGusXuJT3T67sRY0EEoyoO76v350e+8HcrTamLecTtue364yO9nqw5uoQKc=","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 11:06:52] local.INFO: Validation passed  
[2025-07-07 11:06:52] local.INFO: API Domain: http://***********  
[2025-07-07 11:06:52] local.INFO: API Domain: http://***********  
[2025-07-07 11:06:52] local.INFO: Is localhost: false  
[2025-07-07 11:06:52] local.INFO: Port: 80  
[2025-07-07 11:06:52] local.INFO: Is same port: false  
[2025-07-07 11:06:52] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 11:06:52] local.INFO: Request payload: U2FsdGVkX1+bdfuMNidba8ohdk6L2qlYCyh1r4vZi2fKBhUbdGusXuJT3T67sRY0EEoyoO76v350e+8HcrTamLecTtue364yO9nqw5uoQKc=  
[2025-07-07 21:56:27] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 21:56:27] local.INFO: Request data: {"payload":"U2FsdGVkX1+GWzCha7rMaoEENJCSaviJe2K355soPpyzYReBJ20OrAnS9GL03prC+o5zYgl+6WODG8xQnubYoVYyLP912aqbcIZbB5naaGg0o0NwCVpgHF48tUhoR\/B8","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 21:56:29] local.INFO: Validation passed  
[2025-07-07 21:56:29] local.INFO: API Domain: http://***********  
[2025-07-07 21:56:29] local.INFO: API Domain: http://***********  
[2025-07-07 21:56:29] local.INFO: Is localhost: false  
[2025-07-07 21:56:29] local.INFO: Port: 80  
[2025-07-07 21:56:29] local.INFO: Is same port: false  
[2025-07-07 21:56:30] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 21:56:30] local.INFO: Request payload: U2FsdGVkX1+GWzCha7rMaoEENJCSaviJe2K355soPpyzYReBJ20OrAnS9GL03prC+o5zYgl+6WODG8xQnubYoVYyLP912aqbcIZbB5naaGg0o0NwCVpgHF48tUhoR/B8  
[2025-07-07 22:02:14] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 22:02:14] local.INFO: Request data: {"payload":"U2FsdGVkX18Qib9j9yBg\/ZgJNRivSDRGsDjjeoxl2Ael4AqK3ZNvlC7nHYdUg8hoD0ifhD5psjpz+QEcQKwumY6vE5lW2S5Kwzcvz53kItjd9o6LZGDaXKrff+pvoBQq","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 22:02:15] local.INFO: Validation passed  
[2025-07-07 22:02:15] local.INFO: API Domain: http://***********  
[2025-07-07 22:02:15] local.INFO: API Domain: http://***********  
[2025-07-07 22:02:15] local.INFO: Is localhost: false  
[2025-07-07 22:02:15] local.INFO: Port: 80  
[2025-07-07 22:02:15] local.INFO: Is same port: false  
[2025-07-07 22:02:15] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 22:02:15] local.INFO: Request payload: U2FsdGVkX18Qib9j9yBg/ZgJNRivSDRGsDjjeoxl2Ael4AqK3ZNvlC7nHYdUg8hoD0ifhD5psjpz+QEcQKwumY6vE5lW2S5Kwzcvz53kItjd9o6LZGDaXKrff+pvoBQq  
[2025-07-07 22:25:38] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 22:25:38] local.INFO: Request data: {"payload":"U2FsdGVkX184aYYYQhIvj1\/pRXtIcM0i5609Qb8WeiyZlMMc8HWVZkthh3sSTzTnTTlWaHNTfvx+4M1NWlUKcMi8E6Rv8KuSTjXgtom+dYwXw4ionsPAcQwpWPXYrcSx","api_domain":"http:\/\/***********","validated_api_domain":"http:\/\/***********"}  
[2025-07-07 22:25:38] local.INFO: Validation passed  
[2025-07-07 22:25:38] local.INFO: API Domain: http://***********  
[2025-07-07 22:25:38] local.INFO: API Domain: http://***********  
[2025-07-07 22:25:38] local.INFO: Is localhost: false  
[2025-07-07 22:25:38] local.INFO: Port: 80  
[2025-07-07 22:25:38] local.INFO: Is same port: false  
[2025-07-07 22:25:38] local.INFO: Making request to external API: http://***********/admin/api/index.php/api/login  
[2025-07-07 22:25:38] local.INFO: Request payload: U2FsdGVkX184aYYYQhIvj1/pRXtIcM0i5609Qb8WeiyZlMMc8HWVZkthh3sSTzTnTTlWaHNTfvx+4M1NWlUKcMi8E6Rv8KuSTjXgtom+dYwXw4ionsPAcQwpWPXYrcSx  
[2025-07-07 23:01:06] local.INFO: === LOGIN REQUEST STARTED ===  
[2025-07-07 23:01:06] local.INFO: Request data: {"payload":"U2FsdGVkX1\/LsPRMQMTwzWf8yd2dtEmg\/lbi2qVd+111QadH73puN5S1tggbeKqwoJp9pTFHKJ5Pb64VQ8zLZWoZ+llqkiWixlI4Csd\/L3Gz7KvRdcx9sIdA0H5nm7+7","api_domain":"htto:\/\/***********","validated_api_domain":"htto:\/\/***********"}  
[2025-07-07 23:01:07] local.INFO: Validation passed  
[2025-07-07 23:01:07] local.INFO: API Domain: htto://***********  
[2025-07-07 23:01:07] local.INFO: API Domain: htto://***********  
[2025-07-07 23:01:07] local.INFO: Is localhost: false  
[2025-07-07 23:01:07] local.INFO: Port: 80  
[2025-07-07 23:01:07] local.INFO: Is same port: false  
[2025-07-07 23:01:07] local.INFO: Making request to external API: htto://***********/admin/api/index.php/api/login  
[2025-07-07 23:01:07] local.INFO: Request payload: U2FsdGVkX1/LsPRMQMTwzWf8yd2dtEmg/lbi2qVd+111QadH73puN5S1tggbeKqwoJp9pTFHKJ5Pb64VQ8zLZWoZ+llqkiWixlI4Csd/L3Gz7KvRdcx9sIdA0H5nm7+7  
